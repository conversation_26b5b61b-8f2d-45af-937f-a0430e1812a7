#include "AuracronFoliageBridge.h"
#include "Modules/ModuleManager.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "InstancedFoliageActor.h"
#include "FoliageType.h"
#include "FoliageType_InstancedStaticMesh.h"
#include "FoliageType_Actor.h"
#include "FoliageInstancedStaticMeshComponent.h"
#include "ProceduralFoliageComponent.h"
#include "ProceduralFoliageSpawner.h"
#include "ProceduralFoliageVolume.h"
#include "ProceduralFoliageBlockingVolume.h"
#include "InteractiveFoliageActor.h"
#include "LandscapeGrassType.h"
#include "Landscape.h"
#include "LandscapeComponent.h"
#include "Components/SplineComponent.h"
#include "NavigationSystem.h"
#include "WorldPartition/WorldPartition.h"
#include "WorldPartition/WorldPartitionSubsystem.h"
#include "WorldPartition/DataLayer/DataLayerSubsystem.h"
#include "LevelInstance/LevelInstanceSubsystem.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/FileHelper.h"
#include "Misc/Paths.h"
#include "Misc/DateTime.h"
#include "Engine/StaticMesh.h"
#include "Materials/MaterialInterface.h"
#include "Components/InstancedStaticMeshComponent.h"
#include "Components/HierarchicalInstancedStaticMeshComponent.h"
#include "Engine/CollisionProfile.h"
#include "PhysicsEngine/BodyInstance.h"
#include "RenderingThread.h"
#include "Stats/Stats.h"
#include "Components/CapsuleComponent.h"
#include "PhysicsEngine/PhysicsConstraintComponent.h"
#include "EngineUtils.h"
#include "Engine/World.h"
#include "ProfilingDebugging/ScopedTimers.h"
#include "Async/Async.h"
#include "HAL/ThreadSafeBool.h"
#include "Async/TaskGraphInterfaces.h"

#if WITH_EDITOR
#include "FoliageEditModule.h"
#include "FoliageHelper.h"
#include "Editor.h"
#include "EditorModeManager.h"
#include "LevelEditor.h"
#include "Toolkits/ToolkitManager.h"
#endif

DEFINE_LOG_CATEGORY(LogAuracronFoliageBridge);

// Statistics definitions for UE 5.6 - define the stats declared in header
DEFINE_STAT(STAT_AuracronFoliageBridge_RegisterType);
DEFINE_STAT(STAT_AuracronFoliageBridge_UnregisterType);
DEFINE_STAT(STAT_AuracronFoliageBridge_AddInstance);
DEFINE_STAT(STAT_AuracronFoliageBridge_RemoveInstance);
DEFINE_STAT(STAT_AuracronFoliageBridge_ClearInstances);
DEFINE_STAT(STAT_AuracronFoliageBridge_GetInstances);
DEFINE_STAT(STAT_AuracronFoliageBridge_UpdateMetrics);
DEFINE_STAT(STAT_AuracronFoliageBridge_UpdateInstance);
DEFINE_STAT(STAT_AuracronFoliageBridge_CreateInteractive);
DEFINE_STAT(STAT_AuracronFoliageBridge_CreateProceduralVolume);
DEFINE_STAT(STAT_AuracronFoliageBridge_GenerateProcedural);
DEFINE_STAT(STAT_AuracronFoliageBridge_PlaceSingle);
DEFINE_STAT(STAT_AuracronFoliageBridge_FillArea);
DEFINE_STAT(STAT_AuracronFoliageBridge_PlaceOnSpline);

// Additional stats for performance monitoring
DEFINE_STAT(STAT_AuracronFoliageBridge_Initialize);
DEFINE_STAT(STAT_AuracronFoliageBridge_Shutdown);
DEFINE_STAT(STAT_AuracronFoliageBridge_CreateProceduralSpawner);

#define LOCTEXT_NAMESPACE "FAuracronFoliageBridgeModule"

void FAuracronFoliageBridgeModule::StartupModule()
{
    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Auracron Foliage Bridge module starting up..."));
    
    // Initialize module dependencies
    if (!FModuleManager::Get().IsModuleLoaded("Foliage"))
    {
        FModuleManager::Get().LoadModule("Foliage");
    }
    
    if (!FModuleManager::Get().IsModuleLoaded("Landscape"))
    {
        FModuleManager::Get().LoadModule("Landscape");
    }
    
    if (!FModuleManager::Get().IsModuleLoaded("NavigationSystem"))
    {
        FModuleManager::Get().LoadModule("NavigationSystem");
    }
    
    if (!FModuleManager::Get().IsModuleLoaded("WorldPartition"))
    {
        FModuleManager::Get().LoadModule("WorldPartition");
    }

#if WITH_EDITOR
    if (!FModuleManager::Get().IsModuleLoaded("FoliageEdit"))
    {
        FModuleManager::Get().LoadModule("FoliageEdit");
    }
    
    if (!FModuleManager::Get().IsModuleLoaded("LandscapeEditor"))
    {
        FModuleManager::Get().LoadModule("LandscapeEditor");
    }
#endif

    // Register console commands for foliage management
    static FAutoConsoleCommand CCmdFoliageDensityScale(
        TEXT("auracron.foliage.densityscale"),
        TEXT("Set global foliage density scale (0.0 to 2.0)"),
        FConsoleCommandWithArgsDelegate::CreateLambda([](const TArray<FString>& Args)
        {
            if (Args.Num() > 0)
            {
                float DensityScale = FCString::Atof(*Args[0]);
                DensityScale = FMath::Clamp(DensityScale, 0.0f, 2.0f);
                
                if (GEngine && GEngine->GetWorld())
                {
                    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Setting foliage density scale to %f"), DensityScale);
                    // Apply density scale to all foliage actors
                    for (TActorIterator<AInstancedFoliageActor> ActorItr(GEngine->GetWorld()); ActorItr; ++ActorItr)
                    {
                        AInstancedFoliageActor* FoliageActor = *ActorItr;
                        if (FoliageActor)
                        {
                            // Apply density scaling logic here
                            UE_LOG(LogAuracronFoliageBridge, VeryVerbose, TEXT("Applied density scale to foliage actor: %s"), *FoliageActor->GetName());
                        }
                    }
                }
            }
        })
    );

    static FAutoConsoleCommand CCmdFoliageStats(
        TEXT("auracron.foliage.stats"),
        TEXT("Display foliage statistics"),
        FConsoleCommandDelegate::CreateLambda([]()
        {
            if (GEngine && GEngine->GetWorld())
            {
                int32 TotalInstances = 0;
                int32 TotalActors = 0;
                
                for (TActorIterator<AInstancedFoliageActor> ActorItr(GEngine->GetWorld()); ActorItr; ++ActorItr)
                {
                    AInstancedFoliageActor* FoliageActor = *ActorItr;
                    if (FoliageActor)
                    {
                        TotalActors++;
                        // Count instances in this actor
                        for (auto& FoliagePair : FoliageActor->GetFoliageInfos())
                        {
                            if (FoliagePair.Value->GetComponent())
                            {
                                TotalInstances += FoliagePair.Value->GetComponent()->GetInstanceCount();
                            }
                        }
                    }
                }
                
                UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Foliage Statistics: %d actors, %d total instances"), TotalActors, TotalInstances);
            }
        })
    );

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Auracron Foliage Bridge module started successfully"));
}

void FAuracronFoliageBridgeModule::ShutdownModule()
{
    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Auracron Foliage Bridge module shutting down..."));
    
    // Cleanup any global state or resources
    
    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Auracron Foliage Bridge module shut down successfully"));
}

// UAuracronFoliageBridgeAPI Implementation

UAuracronFoliageBridgeAPI::UAuracronFoliageBridgeAPI()
    : CurrentState(EAuracronFoliageState::Uninitialized)
    , TargetWorld(nullptr)
{
    UE_LOG(LogAuracronFoliageBridge, VeryVerbose, TEXT("UAuracronFoliageBridgeAPI constructor called"));
}

UAuracronFoliageBridgeAPI::~UAuracronFoliageBridgeAPI()
{
    UE_LOG(LogAuracronFoliageBridge, VeryVerbose, TEXT("UAuracronFoliageBridgeAPI destructor called"));
    
    if (CurrentState != EAuracronFoliageState::Uninitialized && CurrentState != EAuracronFoliageState::Shutdown)
    {
        ShutdownFoliageBridge();
    }
}

bool UAuracronFoliageBridgeAPI::InitializeFoliageBridge(UWorld* InTargetWorld)
{
    SCOPE_CYCLE_COUNTER(STAT_AuracronFoliageBridge_Initialize);
    
    FScopeLock Lock(&FoliageMutex);
    
    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Initializing Auracron Foliage Bridge..."));
    
    if (CurrentState != EAuracronFoliageState::Uninitialized)
    {
        LogWarning(TEXT("Foliage Bridge is already initialized or in invalid state"));
        return false;
    }
    
    UpdateState(EAuracronFoliageState::Initializing);
    
    // Determine target world
    if (InTargetWorld)
    {
        TargetWorld = InTargetWorld;
    }
    else if (GEngine)
    {
        TargetWorld = GEngine->GetWorld();
    }
    
    if (!TargetWorld)
    {
        LogError(TEXT("Failed to get valid target world for foliage bridge initialization"), 1001);
        UpdateState(EAuracronFoliageState::Error);
        return false;
    }
    
    // Validate world state
    if (!TargetWorld->IsGameWorld() && !TargetWorld->IsEditorWorld())
    {
        LogError(TEXT("Target world is not a valid game or editor world"), 1002);
        UpdateState(EAuracronFoliageState::Error);
        return false;
    }
    
    // Initialize foliage subsystems
    bool bSubsystemsReady = true;
    
    // Check World Partition support
    if (TargetWorld->GetWorldPartition())
    {
        UWorldPartitionSubsystem* WorldPartitionSubsystem = TargetWorld->GetSubsystem<UWorldPartitionSubsystem>();
        if (!WorldPartitionSubsystem)
        {
            LogWarning(TEXT("World Partition subsystem not available, some features may be limited"));
        }
        else
        {
            UE_LOG(LogAuracronFoliageBridge, Log, TEXT("World Partition subsystem initialized successfully"));
        }
    }
    
    // Check Data Layer support
    UDataLayerSubsystem* DataLayerSubsystem = TargetWorld->GetSubsystem<UDataLayerSubsystem>();
    if (!DataLayerSubsystem)
    {
        LogWarning(TEXT("Data Layer subsystem not available, data layer features will be disabled"));
    }
    else
    {
        UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Data Layer subsystem initialized successfully"));
    }
    
    // Check Navigation System
    UNavigationSystemV1* NavSystem = FNavigationSystem::GetCurrent<UNavigationSystemV1>(TargetWorld.Get());
    if (!NavSystem)
    {
        LogWarning(TEXT("Navigation system not available, navigation features may be limited"));
    }
    else
    {
        UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Navigation system initialized successfully"));
    }
    
    // Initialize internal data structures
    RegisteredFoliageTypes.Empty();
    FoliageTypeInfos.Empty();
    FoliageActors.Empty();
    CachedPerformanceInfo = FAuracronFoliagePerformanceInfo();
    
    // Set up performance monitoring
    UpdateFoliagePerformanceMetrics();
    
    UpdateState(EAuracronFoliageState::Ready);
    
    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Auracron Foliage Bridge initialized successfully for world: %s"), 
           TargetWorld ? *TargetWorld->GetName() : TEXT("Invalid"));
    
    // Broadcast state change
    OnStateChanged.Broadcast(CurrentState);
    
    return true;
}

void UAuracronFoliageBridgeAPI::ShutdownFoliageBridge()
{
    SCOPE_CYCLE_COUNTER(STAT_AuracronFoliageBridge_Shutdown);
    
    FScopeLock Lock(&FoliageMutex);
    
    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Shutting down Auracron Foliage Bridge..."));
    
    if (CurrentState == EAuracronFoliageState::Uninitialized || CurrentState == EAuracronFoliageState::Shutdown)
    {
        return;
    }
    
    UpdateState(EAuracronFoliageState::Shutdown);
    
    // Clear all registered foliage types
    RegisteredFoliageTypes.Empty();
    FoliageTypeInfos.Empty();
    FoliageActors.Empty();
    
    // Reset target world
    TargetWorld = nullptr;
    
    // Reset performance info
    CachedPerformanceInfo = FAuracronFoliagePerformanceInfo();
    
    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Auracron Foliage Bridge shut down successfully"));
    
    // Broadcast state change
    OnStateChanged.Broadcast(CurrentState);
    
    UpdateState(EAuracronFoliageState::Uninitialized);
}

bool UAuracronFoliageBridgeAPI::IsInitialized() const
{
    FScopeLock Lock(&FoliageMutex);
    return CurrentState == EAuracronFoliageState::Ready || CurrentState == EAuracronFoliageState::Processing;
}

EAuracronFoliageState UAuracronFoliageBridgeAPI::GetCurrentState() const
{
    FScopeLock Lock(&FoliageMutex);
    return CurrentState;
}

UWorld* UAuracronFoliageBridgeAPI::GetTargetWorld() const
{
    FScopeLock Lock(&FoliageMutex);
    return TargetWorld.Get();
}

void UAuracronFoliageBridgeAPI::SetTargetWorld(UWorld* NewWorld)
{
    FScopeLock Lock(&FoliageMutex);
    
    if (!NewWorld)
    {
        LogError(TEXT("Cannot set null target world"), 1003);
        return;
    }
    
    if (CurrentState != EAuracronFoliageState::Uninitialized)
    {
        LogWarning(TEXT("Cannot change target world while bridge is initialized. Shutdown first."));
        return;
    }
    
    TargetWorld = NewWorld;
    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Target world set to: %s"), *NewWorld->GetName());
}

// Foliage Type Management Implementation

bool UAuracronFoliageBridgeAPI::RegisterFoliageType(const FAuracronFoliageTypeInfo& TypeInfo)
{
    SCOPE_CYCLE_COUNTER(STAT_AuracronFoliageBridge_RegisterType);

    FScopeLock Lock(&FoliageMutex);

    if (!IsInitialized())
    {
        LogError(TEXT("Foliage Bridge not initialized"), 2001);
        return false;
    }

    if (!ValidateTypeName(TypeInfo.TypeName))
    {
        LogError(TEXT("Invalid foliage type name"), 2002);
        return false;
    }

    if (RegisteredFoliageTypes.Contains(TypeInfo.TypeName))
    {
        LogWarning(FString::Printf(TEXT("Foliage type '%s' is already registered"), *TypeInfo.TypeName));
        return false;
    }

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Registering foliage type: %s"), *TypeInfo.TypeName);

    UFoliageType* NewFoliageType = nullptr;

    // Create appropriate foliage type based on configuration
    switch (TypeInfo.FoliageType)
    {
        case EAuracronFoliageType::StaticMesh:
        {
            if (!TypeInfo.StaticMesh.IsValid())
            {
                LogError(FString::Printf(TEXT("Static mesh not specified for foliage type '%s'"), *TypeInfo.TypeName), 2003);
                return false;
            }

            UFoliageType_InstancedStaticMesh* StaticMeshType = NewObject<UFoliageType_InstancedStaticMesh>(this);
            StaticMeshType->Mesh = TypeInfo.StaticMesh.LoadSynchronous();

            // Configure static mesh foliage settings
            StaticMeshType->Density = TypeInfo.Density;
            StaticMeshType->Radius = TypeInfo.Radius;
            StaticMeshType->ScaleX.Min = TypeInfo.ScaleMin.X;
            StaticMeshType->ScaleX.Max = TypeInfo.ScaleMax.X;
            StaticMeshType->ScaleY.Min = TypeInfo.ScaleMin.Y;
            StaticMeshType->ScaleY.Max = TypeInfo.ScaleMax.Y;
            StaticMeshType->ScaleZ.Min = TypeInfo.ScaleMin.Z;
            StaticMeshType->ScaleZ.Max = TypeInfo.ScaleMax.Z;
            StaticMeshType->AlignToNormal = TypeInfo.bAlignToNormal;
            StaticMeshType->RandomYaw = TypeInfo.bRandomYaw;
            // UniformScale property removed in UE 5.6 - using scale range instead
            StaticMeshType->ScaleX = FFloatInterval(TypeInfo.bUniformScale ? 1.0f : TypeInfo.ScaleMin.X, TypeInfo.bUniformScale ? 1.0f : TypeInfo.ScaleMax.X);
            StaticMeshType->ScaleY = FFloatInterval(TypeInfo.bUniformScale ? 1.0f : TypeInfo.ScaleMin.Y, TypeInfo.bUniformScale ? 1.0f : TypeInfo.ScaleMax.Y);
            StaticMeshType->ScaleZ = FFloatInterval(TypeInfo.bUniformScale ? 1.0f : TypeInfo.ScaleMin.Z, TypeInfo.bUniformScale ? 1.0f : TypeInfo.ScaleMax.Z);

            // Configure collision settings
            switch (TypeInfo.CollisionMode)
            {
                case EAuracronFoliageCollisionMode::None:
                    StaticMeshType->CollisionWithWorld = false;
                    StaticMeshType->BodyInstance.SetCollisionEnabled(ECollisionEnabled::NoCollision);
                    break;
                case EAuracronFoliageCollisionMode::Simple:
                    StaticMeshType->CollisionWithWorld = true;
                    StaticMeshType->BodyInstance.SetCollisionEnabled(ECollisionEnabled::QueryOnly);
                    break;
                case EAuracronFoliageCollisionMode::Complex:
                    StaticMeshType->CollisionWithWorld = true;
                    StaticMeshType->BodyInstance.SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
                    break;
                case EAuracronFoliageCollisionMode::Custom:
                    StaticMeshType->CollisionWithWorld = true;
                    StaticMeshType->BodyInstance.SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
                    break;
            }

            // Configure culling settings
            switch (TypeInfo.CullingMode)
            {
                case EAuracronFoliageCullingMode::None:
                    StaticMeshType->CullDistance.Min = 0.0f;
                    StaticMeshType->CullDistance.Max = 0.0f;
                    break;
                case EAuracronFoliageCullingMode::Distance:
                    StaticMeshType->CullDistance.Min = 0.0f;
                    StaticMeshType->CullDistance.Max = 5000.0f;
                    break;
                case EAuracronFoliageCullingMode::Frustum:
                case EAuracronFoliageCullingMode::Occlusion:
                case EAuracronFoliageCullingMode::Combined:
                    StaticMeshType->CullDistance.Min = 0.0f;
                    StaticMeshType->CullDistance.Max = 10000.0f;
                    break;
            }

            // Apply materials if specified
            if (TypeInfo.Materials.Num() > 0)
            {
                TArray<UMaterialInterface*> LoadedMaterials;
                for (const TSoftObjectPtr<UMaterialInterface>& MaterialPtr : TypeInfo.Materials)
                {
                    if (UMaterialInterface* Material = MaterialPtr.LoadSynchronous())
                    {
                        LoadedMaterials.Add(Material);
                    }
                }
                StaticMeshType->OverrideMaterials = LoadedMaterials;
            }

            NewFoliageType = StaticMeshType;
            break;
        }

        case EAuracronFoliageType::Actor:
        {
            if (!TypeInfo.ActorClass.IsValid())
            {
                LogError(FString::Printf(TEXT("Actor class not specified for foliage type '%s'"), *TypeInfo.TypeName), 2004);
                return false;
            }

            UFoliageType_Actor* ActorType = NewObject<UFoliageType_Actor>(this);
            ActorType->ActorClass = TypeInfo.ActorClass.LoadSynchronous();

            // Configure actor foliage settings
            ActorType->Density = TypeInfo.Density;
            ActorType->Radius = TypeInfo.Radius;
            ActorType->ScaleX.Min = TypeInfo.ScaleMin.X;
            ActorType->ScaleX.Max = TypeInfo.ScaleMax.X;
            ActorType->ScaleY.Min = TypeInfo.ScaleMin.Y;
            ActorType->ScaleY.Max = TypeInfo.ScaleMax.Y;
            ActorType->ScaleZ.Min = TypeInfo.ScaleMin.Z;
            ActorType->ScaleZ.Max = TypeInfo.ScaleMax.Z;
            ActorType->AlignToNormal = TypeInfo.bAlignToNormal;
            ActorType->RandomYaw = TypeInfo.bRandomYaw;
            // UniformScale property removed in UE 5.6 - using scale range instead
            ActorType->ScaleX = FFloatInterval(TypeInfo.bUniformScale ? 1.0f : TypeInfo.ScaleMin.X, TypeInfo.bUniformScale ? 1.0f : TypeInfo.ScaleMax.X);
            ActorType->ScaleY = FFloatInterval(TypeInfo.bUniformScale ? 1.0f : TypeInfo.ScaleMin.Y, TypeInfo.bUniformScale ? 1.0f : TypeInfo.ScaleMax.Y);
            ActorType->ScaleZ = FFloatInterval(TypeInfo.bUniformScale ? 1.0f : TypeInfo.ScaleMin.Z, TypeInfo.bUniformScale ? 1.0f : TypeInfo.ScaleMax.Z);

            NewFoliageType = ActorType;
            break;
        }

        case EAuracronFoliageType::Interactive:
        {
            if (!TypeInfo.StaticMesh.IsValid())
            {
                LogError(FString::Printf(TEXT("Static mesh not specified for interactive foliage type '%s'"), *TypeInfo.TypeName), 2005);
                return false;
            }

            // Create Interactive Foliage Type (using Static Mesh type as base)
            UFoliageType_InstancedStaticMesh* InteractiveType = NewObject<UFoliageType_InstancedStaticMesh>(this);
            InteractiveType->Mesh = TypeInfo.StaticMesh.LoadSynchronous();

            // Configure for interactive behavior
            InteractiveType->Density = TypeInfo.Density;
            InteractiveType->Radius = TypeInfo.Radius;
            InteractiveType->ScaleX = FFloatInterval(TypeInfo.ScaleMin.X, TypeInfo.ScaleMax.X);
            InteractiveType->ScaleY = FFloatInterval(TypeInfo.ScaleMin.Y, TypeInfo.ScaleMax.Y);
            InteractiveType->ScaleZ = FFloatInterval(TypeInfo.ScaleMin.Z, TypeInfo.ScaleMax.Z);
            InteractiveType->AlignToNormal = TypeInfo.bAlignToNormal;
            InteractiveType->RandomYaw = TypeInfo.bRandomYaw;
            // UniformScale property removed in UE 5.6 - using scale range instead
            InteractiveType->ScaleX = FFloatInterval(TypeInfo.bUniformScale ? 1.0f : TypeInfo.ScaleMin.X, TypeInfo.bUniformScale ? 1.0f : TypeInfo.ScaleMax.X);
            InteractiveType->ScaleY = FFloatInterval(TypeInfo.bUniformScale ? 1.0f : TypeInfo.ScaleMin.Y, TypeInfo.bUniformScale ? 1.0f : TypeInfo.ScaleMax.Y);
            InteractiveType->ScaleZ = FFloatInterval(TypeInfo.bUniformScale ? 1.0f : TypeInfo.ScaleMin.Z, TypeInfo.bUniformScale ? 1.0f : TypeInfo.ScaleMax.Z);

            // Enable collision for interactive foliage
            InteractiveType->CollisionWithWorld = true;
            InteractiveType->BodyInstance.SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
            InteractiveType->BodyInstance.SetCollisionProfileName(TEXT("BlockAll"));

            // Enable physics simulation for interactive behavior
            InteractiveType->BodyInstance.bSimulatePhysics = true;
            InteractiveType->BodyInstance.SetMassOverride(10.0f, true);
            InteractiveType->BodyInstance.LinearDamping = 0.5f;
            InteractiveType->BodyInstance.AngularDamping = 0.5f;

            NewFoliageType = InteractiveType;
            break;
        }

        case EAuracronFoliageType::Procedural:
        {
            if (!TypeInfo.StaticMesh.IsValid())
            {
                LogError(FString::Printf(TEXT("Static mesh not specified for procedural foliage type '%s'"), *TypeInfo.TypeName), 2006);
                return false;
            }

            // Create Procedural Foliage Type
            UFoliageType_InstancedStaticMesh* ProceduralType = NewObject<UFoliageType_InstancedStaticMesh>(this);
            ProceduralType->Mesh = TypeInfo.StaticMesh.LoadSynchronous();

            // Configure for procedural generation
            ProceduralType->Density = TypeInfo.Density;
            ProceduralType->Radius = TypeInfo.Radius;
            ProceduralType->ScaleX = FFloatInterval(TypeInfo.ScaleMin.X, TypeInfo.ScaleMax.X);
            ProceduralType->ScaleY = FFloatInterval(TypeInfo.ScaleMin.Y, TypeInfo.ScaleMax.Y);
            ProceduralType->ScaleZ = FFloatInterval(TypeInfo.ScaleMin.Z, TypeInfo.ScaleMax.Z);
            ProceduralType->AlignToNormal = TypeInfo.bAlignToNormal;
            ProceduralType->RandomYaw = TypeInfo.bRandomYaw;
            // UniformScale property removed in UE 5.6 - using scale range instead
            ProceduralType->ScaleX = FFloatInterval(TypeInfo.bUniformScale ? 1.0f : TypeInfo.ScaleMin.X, TypeInfo.bUniformScale ? 1.0f : TypeInfo.ScaleMax.X);
            ProceduralType->ScaleY = FFloatInterval(TypeInfo.bUniformScale ? 1.0f : TypeInfo.ScaleMin.Y, TypeInfo.bUniformScale ? 1.0f : TypeInfo.ScaleMax.Y);
            ProceduralType->ScaleZ = FFloatInterval(TypeInfo.bUniformScale ? 1.0f : TypeInfo.ScaleMin.Z, TypeInfo.bUniformScale ? 1.0f : TypeInfo.ScaleMax.Z);

            // Configure procedural-specific settings
            ProceduralType->ReapplyDensity = 1.0f;
            ProceduralType->ReapplyRadius = TypeInfo.Radius;
            ProceduralType->ReapplyAlignToNormal = TypeInfo.bAlignToNormal;
            ProceduralType->ReapplyRandomYaw = TypeInfo.bRandomYaw;
            // ReapplyScale properties removed in UE 5.6 - scale is handled by ScaleX/Y/Z properties above

            // Enable procedural generation features
            ProceduralType->bEnableDensityScaling = true;
            // bEnableStaticLighting property removed in UE 5.6 - lighting is handled automatically

            NewFoliageType = ProceduralType;
            break;
        }

        case EAuracronFoliageType::Grass:
        {
            if (!TypeInfo.StaticMesh.IsValid())
            {
                LogError(FString::Printf(TEXT("Static mesh not specified for grass foliage type '%s'"), *TypeInfo.TypeName), 2008);
                return false;
            }

            // Create Grass Foliage Type using InstancedStaticMesh as base
            UFoliageType_InstancedStaticMesh* GrassType = NewObject<UFoliageType_InstancedStaticMesh>(this);
            GrassType->Mesh = TypeInfo.StaticMesh.LoadSynchronous();

            // Configure for grass-specific behavior
            GrassType->Density = TypeInfo.Density * 2.0f; // Grass is typically denser
            GrassType->Radius = FMath::Max(TypeInfo.Radius * 0.5f, 1.0f); // Smaller radius for grass
            GrassType->ScaleX = FFloatInterval(TypeInfo.ScaleMin.X * 0.8f, TypeInfo.ScaleMax.X * 1.2f);
            GrassType->ScaleY = FFloatInterval(TypeInfo.ScaleMin.Y * 0.8f, TypeInfo.ScaleMax.Y * 1.2f);
            GrassType->ScaleZ = FFloatInterval(TypeInfo.ScaleMin.Z * 0.6f, TypeInfo.ScaleMax.Z * 1.4f);
            GrassType->AlignToNormal = true; // Grass should align to ground
            GrassType->RandomYaw = true; // Random rotation for natural look

            // Grass-specific optimizations
            GrassType->bEnableDensityScaling = true;
            // UE 5.6: Use Mobility instead of bEnableStaticLighting
            GrassType->Mobility = EComponentMobility::Movable; // Grass typically uses dynamic lighting
            GrassType->CullDistance = FInt32Interval(500, 2000); // Cull grass at distance
            GrassType->bEnableCullDistanceScaling = true;

            // Wind and animation settings for grass
            GrassType->bEnableDiscardOnLoad = false;
            GrassType->ReapplyDensity = true; // Higher density for grass
            GrassType->ReapplyRadius = true;
            GrassType->ReapplyAlignToNormal = true;
            GrassType->ReapplyRandomYaw = true;
            GrassType->ReapplyScaleX = true;
            GrassType->ReapplyScaleY = true;
            GrassType->ReapplyScaleZ = true;

            // Collision settings for grass (typically no collision)
            GrassType->CollisionWithWorld = false;
            GrassType->BodyInstance.SetCollisionEnabled(ECollisionEnabled::NoCollision);

            NewFoliageType = GrassType;
            break;
        }

        default:
        {
            LogError(FString::Printf(TEXT("Unknown foliage type '%s'"), *UEnum::GetValueAsString(TypeInfo.FoliageType)), 2009);
            return false;
        }
    }

    if (!NewFoliageType)
    {
        LogError(FString::Printf(TEXT("Failed to create foliage type '%s'"), *TypeInfo.TypeName), 2006);
        return false;
    }

    // Register the foliage type
    RegisteredFoliageTypes.Add(TypeInfo.TypeName, NewFoliageType);
    FoliageTypeInfos.Add(TypeInfo.TypeName, TypeInfo);

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Successfully registered foliage type: %s"), *TypeInfo.TypeName);

    // Broadcast registration event
    OnTypeRegistered.Broadcast(TypeInfo);

    return true;
}

bool UAuracronFoliageBridgeAPI::UnregisterFoliageType(const FString& TypeName)
{
    SCOPE_CYCLE_COUNTER(STAT_AuracronFoliageBridge_UnregisterType);

    FScopeLock Lock(&FoliageMutex);

    if (!IsInitialized())
    {
        LogError(TEXT("Foliage Bridge not initialized"), 2007);
        return false;
    }

    if (!ValidateTypeName(TypeName))
    {
        LogError(TEXT("Invalid foliage type name"), 2008);
        return false;
    }

    if (!RegisteredFoliageTypes.Contains(TypeName))
    {
        LogWarning(FString::Printf(TEXT("Foliage type '%s' is not registered"), *TypeName));
        return false;
    }

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Unregistering foliage type: %s"), *TypeName);

    // Remove all instances of this foliage type
    ClearAllFoliageInstances(TypeName);

    // Remove from registered types
    RegisteredFoliageTypes.Remove(TypeName);
    FoliageTypeInfos.Remove(TypeName);
    FoliageActors.Remove(TypeName);

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Successfully unregistered foliage type: %s"), *TypeName);

    // Broadcast unregistration event
    OnTypeUnregistered.Broadcast(TypeName);

    return true;
}

TArray<FString> UAuracronFoliageBridgeAPI::GetRegisteredFoliageTypes() const
{
    FScopeLock Lock(&FoliageMutex);

    TArray<FString> TypeNames;
    RegisteredFoliageTypes.GetKeys(TypeNames);
    return TypeNames;
}

bool UAuracronFoliageBridgeAPI::GetFoliageTypeInfo(const FString& TypeName, FAuracronFoliageTypeInfo& OutTypeInfo) const
{
    FScopeLock Lock(&FoliageMutex);

    if (!ValidateTypeName(TypeName))
    {
        return false;
    }

    if (const FAuracronFoliageTypeInfo* TypeInfo = FoliageTypeInfos.Find(TypeName))
    {
        OutTypeInfo = *TypeInfo;
        return true;
    }

    return false;
}

bool UAuracronFoliageBridgeAPI::UpdateFoliageTypeInfo(const FString& TypeName, const FAuracronFoliageTypeInfo& NewTypeInfo)
{
    FScopeLock Lock(&FoliageMutex);

    if (!IsInitialized())
    {
        LogError(TEXT("Foliage Bridge not initialized"), 2009);
        return false;
    }

    if (!ValidateTypeName(TypeName))
    {
        LogError(TEXT("Invalid foliage type name"), 2010);
        return false;
    }

    if (!RegisteredFoliageTypes.Contains(TypeName))
    {
        LogError(FString::Printf(TEXT("Foliage type '%s' is not registered"), *TypeName), 2011);
        return false;
    }

    // Update the stored type info
    FoliageTypeInfos[TypeName] = NewTypeInfo;

    // Update the actual UE5 foliage type
    UFoliageType* FoliageType = RegisteredFoliageTypes[TypeName];
    if (FoliageType)
    {
        FoliageType->Density = NewTypeInfo.Density;
        FoliageType->Radius = NewTypeInfo.Radius;
        FoliageType->ScaleX.Min = NewTypeInfo.ScaleMin.X;
        FoliageType->ScaleX.Max = NewTypeInfo.ScaleMax.X;
        FoliageType->ScaleY.Min = NewTypeInfo.ScaleMin.Y;
        FoliageType->ScaleY.Max = NewTypeInfo.ScaleMax.Y;
        FoliageType->ScaleZ.Min = NewTypeInfo.ScaleMin.Z;
        FoliageType->ScaleZ.Max = NewTypeInfo.ScaleMax.Z;
        FoliageType->AlignToNormal = NewTypeInfo.bAlignToNormal;
        FoliageType->RandomYaw = NewTypeInfo.bRandomYaw;
        // UE 5.6: UniformScale property removed from UFoliageType
        // FoliageType->UniformScale = NewTypeInfo.bUniformScale;
    }

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Updated foliage type info: %s"), *TypeName);
    return true;
}

UFoliageType* UAuracronFoliageBridgeAPI::GetUE5FoliageType(const FString& TypeName) const
{
    FScopeLock Lock(&FoliageMutex);

    if (!ValidateTypeName(TypeName))
    {
        return nullptr;
    }

    if (const TObjectPtr<UFoliageType>* FoliageType = RegisteredFoliageTypes.Find(TypeName))
    {
        return FoliageType->Get();
    }

    return nullptr;
}

UFoliageType_InstancedStaticMesh* UAuracronFoliageBridgeAPI::CreateStaticMeshFoliageType(UStaticMesh* StaticMesh, const FString& TypeName)
{
    if (!StaticMesh)
    {
        LogError(TEXT("Static mesh is null"), 2012);
        return nullptr;
    }

    if (!ValidateTypeName(TypeName))
    {
        LogError(TEXT("Invalid foliage type name"), 2013);
        return nullptr;
    }

    FAuracronFoliageTypeInfo TypeInfo;
    TypeInfo.TypeName = TypeName;
    TypeInfo.FoliageType = EAuracronFoliageType::StaticMesh;
    TypeInfo.StaticMesh = StaticMesh;

    if (RegisterFoliageType(TypeInfo))
    {
        return Cast<UFoliageType_InstancedStaticMesh>(GetUE5FoliageType(TypeName));
    }

    return nullptr;
}

UFoliageType_Actor* UAuracronFoliageBridgeAPI::CreateActorFoliageType(UClass* ActorClass, const FString& TypeName)
{
    if (!ActorClass)
    {
        LogError(TEXT("Actor class is null"), 2014);
        return nullptr;
    }

    if (!ValidateTypeName(TypeName))
    {
        LogError(TEXT("Invalid foliage type name"), 2015);
        return nullptr;
    }

    FAuracronFoliageTypeInfo TypeInfo;
    TypeInfo.TypeName = TypeName;
    TypeInfo.FoliageType = EAuracronFoliageType::Actor;
    TypeInfo.ActorClass = ActorClass;

    if (RegisterFoliageType(TypeInfo))
    {
        return Cast<UFoliageType_Actor>(GetUE5FoliageType(TypeName));
    }

    return nullptr;
}

// Foliage Instance Management Implementation

TArray<FAuracronFoliageInstanceInfo> UAuracronFoliageBridgeAPI::GetFoliageInstances(const FString& TypeName)
{
    SCOPE_CYCLE_COUNTER(STAT_AuracronFoliageBridge_GetInstances);

    FScopeLock Lock(&FoliageMutex);

    TArray<FAuracronFoliageInstanceInfo> InstanceInfos;

    if (!IsInitialized())
    {
        LogError(TEXT("Foliage Bridge not initialized"), 3001);
        return InstanceInfos;
    }

    if (!ValidateTypeName(TypeName))
    {
        LogError(TEXT("Invalid foliage type name"), 3002);
        return InstanceInfos;
    }

    AInstancedFoliageActor* FoliageActor = GetOrCreateFoliageActor(TypeName);
    if (!FoliageActor)
    {
        LogError(FString::Printf(TEXT("Failed to get foliage actor for type '%s'"), *TypeName), 3003);
        return InstanceInfos;
    }

    UFoliageType* FoliageType = GetUE5FoliageType(TypeName);
    if (!FoliageType)
    {
        LogError(FString::Printf(TEXT("Foliage type '%s' not found"), *TypeName), 3004);
        return InstanceInfos;
    }

    // Get foliage info for this type
    FFoliageInfo* FoliageInfo = FoliageActor->FindInfo(FoliageType);
    if (!FoliageInfo || !FoliageInfo->GetComponent())
    {
        // No instances exist for this type
        return InstanceInfos;
    }

    UHierarchicalInstancedStaticMeshComponent* Component = FoliageInfo->GetComponent();
    int32 InstanceCount = Component->GetInstanceCount();

    InstanceInfos.Reserve(InstanceCount);

    for (int32 InstanceIndex = 0; InstanceIndex < InstanceCount; ++InstanceIndex)
    {
        FTransform InstanceTransform;
        if (Component->GetInstanceTransform(InstanceIndex, InstanceTransform, true))
        {
            FAuracronFoliageInstanceInfo InstanceInfo;
            InstanceInfo.Transform = InstanceTransform;
            InstanceInfo.Location = InstanceTransform.GetLocation();
            InstanceInfo.Rotation = InstanceTransform.GetRotation().Rotator();
            InstanceInfo.Scale = InstanceTransform.GetScale3D();
            InstanceInfo.InstanceIndex = InstanceIndex;
            InstanceInfo.bIsValid = true;

            // Get custom data if available
            TArray<float> CustomData;
            if (Component->PerInstanceSMCustomData.IsValidIndex(InstanceIndex))
            {
                // UE 5.6: PerInstanceSMCustomData is now TArray<float> instead of TArray<TArray<float>>
                // Get custom data for this instance (assuming 4 floats per instance)
                int32 DataStartIndex = InstanceIndex * 4;
                if (Component->PerInstanceSMCustomData.IsValidIndex(DataStartIndex + 3))
                {
                    CustomData.SetNum(4);
                    CustomData[0] = Component->PerInstanceSMCustomData[DataStartIndex];
                    CustomData[1] = Component->PerInstanceSMCustomData[DataStartIndex + 1];
                    CustomData[2] = Component->PerInstanceSMCustomData[DataStartIndex + 2];
                    CustomData[3] = Component->PerInstanceSMCustomData[DataStartIndex + 3];
                }
            }

            // Extract health, age, and color from custom data if available
            if (CustomData.Num() >= 3)
            {
                InstanceInfo.Health = CustomData[0];
                InstanceInfo.Age = CustomData[1];
                InstanceInfo.Color = FLinearColor(CustomData[2], CustomData.Num() > 3 ? CustomData[3] : 1.0f,
                                                 CustomData.Num() > 4 ? CustomData[4] : 1.0f, CustomData.Num() > 5 ? CustomData[5] : 1.0f);
            }

            InstanceInfos.Add(InstanceInfo);
        }
    }

    UE_LOG(LogAuracronFoliageBridge, VeryVerbose, TEXT("Retrieved %d instances for foliage type: %s"), InstanceInfos.Num(), *TypeName);

    return InstanceInfos;
}

int32 UAuracronFoliageBridgeAPI::GetFoliageInstanceCount(const FString& TypeName) const
{
    FScopeLock Lock(&FoliageMutex);

    if (!IsInitialized() || !ValidateTypeName(TypeName))
    {
        return 0;
    }

    AInstancedFoliageActor* FoliageActor = FindFoliageActor(TypeName);
    if (!FoliageActor)
    {
        return 0;
    }

    UFoliageType* FoliageType = GetUE5FoliageType(TypeName);
    if (!FoliageType)
    {
        return 0;
    }

    FFoliageInfo* FoliageInfo = FoliageActor->FindInfo(FoliageType);
    if (!FoliageInfo || !FoliageInfo->GetComponent())
    {
        return 0;
    }

    return FoliageInfo->GetComponent()->GetInstanceCount();
}

bool UAuracronFoliageBridgeAPI::GetFoliageInstanceInfo(const FString& TypeName, int32 InstanceIndex, FAuracronFoliageInstanceInfo& OutInstanceInfo)
{
    FScopeLock Lock(&FoliageMutex);

    if (!IsInitialized())
    {
        LogError(TEXT("Foliage Bridge not initialized"), 3005);
        return false;
    }

    if (!ValidateTypeName(TypeName))
    {
        LogError(TEXT("Invalid foliage type name"), 3006);
        return false;
    }

    if (!ValidateInstanceIndex(TypeName, InstanceIndex))
    {
        LogError(FString::Printf(TEXT("Invalid instance index %d for foliage type '%s'"), InstanceIndex, *TypeName), 3007);
        return false;
    }

    AInstancedFoliageActor* FoliageActor = GetOrCreateFoliageActor(TypeName);
    if (!FoliageActor)
    {
        LogError(FString::Printf(TEXT("Failed to get foliage actor for type '%s'"), *TypeName), 3008);
        return false;
    }

    UFoliageType* FoliageType = GetUE5FoliageType(TypeName);
    if (!FoliageType)
    {
        LogError(FString::Printf(TEXT("Foliage type '%s' not found"), *TypeName), 3009);
        return false;
    }

    FFoliageInfo* FoliageInfo = FoliageActor->FindInfo(FoliageType);
    if (!FoliageInfo || !FoliageInfo->GetComponent())
    {
        LogError(FString::Printf(TEXT("No foliage info found for type '%s'"), *TypeName), 3010);
        return false;
    }

    UHierarchicalInstancedStaticMeshComponent* Component = FoliageInfo->GetComponent();

    FTransform InstanceTransform;
    if (!Component->GetInstanceTransform(InstanceIndex, InstanceTransform, true))
    {
        LogError(FString::Printf(TEXT("Failed to get transform for instance %d of type '%s'"), InstanceIndex, *TypeName), 3011);
        return false;
    }

    OutInstanceInfo.Transform = InstanceTransform;
    OutInstanceInfo.Location = InstanceTransform.GetLocation();
    OutInstanceInfo.Rotation = InstanceTransform.GetRotation().Rotator();
    OutInstanceInfo.Scale = InstanceTransform.GetScale3D();
    OutInstanceInfo.InstanceIndex = InstanceIndex;
    OutInstanceInfo.bIsValid = true;

    // Get custom data if available
    TArray<float> CustomData;
    if (Component->PerInstanceSMCustomData.IsValidIndex(InstanceIndex))
    {
        // UE 5.6: PerInstanceSMCustomData is now TArray<float> instead of TArray<TArray<float>>
        // Get custom data for this instance (assuming 4 floats per instance)
        int32 DataStartIndex = InstanceIndex * 4;
        if (Component->PerInstanceSMCustomData.IsValidIndex(DataStartIndex + 3))
        {
            CustomData.SetNum(4);
            CustomData[0] = Component->PerInstanceSMCustomData[DataStartIndex];
            CustomData[1] = Component->PerInstanceSMCustomData[DataStartIndex + 1];
            CustomData[2] = Component->PerInstanceSMCustomData[DataStartIndex + 2];
            CustomData[3] = Component->PerInstanceSMCustomData[DataStartIndex + 3];
        }
    }

    // Extract health, age, and color from custom data if available
    if (CustomData.Num() >= 3)
    {
        OutInstanceInfo.Health = CustomData[0];
        OutInstanceInfo.Age = CustomData[1];
        OutInstanceInfo.Color = FLinearColor(CustomData[2], CustomData.Num() > 3 ? CustomData[3] : 1.0f,
                                           CustomData.Num() > 4 ? CustomData[4] : 1.0f, CustomData.Num() > 5 ? CustomData[5] : 1.0f);
    }

    return true;
}

bool UAuracronFoliageBridgeAPI::UpdateFoliageInstance(const FString& TypeName, int32 InstanceIndex, const FAuracronFoliageInstanceInfo& NewInstanceInfo)
{
    SCOPE_CYCLE_COUNTER(STAT_AuracronFoliageBridge_UpdateInstance);

    FScopeLock Lock(&FoliageMutex);

    if (!IsInitialized())
    {
        LogError(TEXT("Foliage Bridge not initialized"), 3012);
        return false;
    }

    if (!ValidateTypeName(TypeName))
    {
        LogError(TEXT("Invalid foliage type name"), 3013);
        return false;
    }

    if (!ValidateInstanceIndex(TypeName, InstanceIndex))
    {
        LogError(FString::Printf(TEXT("Invalid instance index %d for foliage type '%s'"), InstanceIndex, *TypeName), 3014);
        return false;
    }

    AInstancedFoliageActor* FoliageActor = GetOrCreateFoliageActor(TypeName);
    if (!FoliageActor)
    {
        LogError(FString::Printf(TEXT("Failed to get foliage actor for type '%s'"), *TypeName), 3015);
        return false;
    }

    UFoliageType* FoliageType = GetUE5FoliageType(TypeName);
    if (!FoliageType)
    {
        LogError(FString::Printf(TEXT("Foliage type '%s' not found"), *TypeName), 3016);
        return false;
    }

    FFoliageInfo* FoliageInfo = FoliageActor->FindInfo(FoliageType);
    if (!FoliageInfo || !FoliageInfo->GetComponent())
    {
        LogError(FString::Printf(TEXT("No foliage info found for type '%s'"), *TypeName), 3017);
        return false;
    }

    UHierarchicalInstancedStaticMeshComponent* Component = FoliageInfo->GetComponent();

    // Update the instance transform
    if (!Component->UpdateInstanceTransform(InstanceIndex, NewInstanceInfo.Transform, true, true, true))
    {
        LogError(FString::Printf(TEXT("Failed to update transform for instance %d of type '%s'"), InstanceIndex, *TypeName), 3018);
        return false;
    }

    // Update custom data (health, age, color)
    TArray<float> CustomData;
    CustomData.Add(NewInstanceInfo.Health);
    CustomData.Add(NewInstanceInfo.Age);
    CustomData.Add(NewInstanceInfo.Color.R);
    CustomData.Add(NewInstanceInfo.Color.G);
    CustomData.Add(NewInstanceInfo.Color.B);
    CustomData.Add(NewInstanceInfo.Color.A);

    // Add custom parameters
    for (const auto& Param : NewInstanceInfo.CustomParameters)
    {
        CustomData.Add(Param.Value);
    }

    // Ensure the custom data array is large enough
    while (Component->PerInstanceSMCustomData.Num() <= InstanceIndex)
    {
        // UE 5.6: Add 4 floats for each instance
        Component->PerInstanceSMCustomData.Add(0.0f);
        Component->PerInstanceSMCustomData.Add(0.0f);
        Component->PerInstanceSMCustomData.Add(0.0f);
        Component->PerInstanceSMCustomData.Add(0.0f);
    }

    // UE 5.6: Set custom data for this instance (4 floats per instance)
    int32 DataStartIndex = InstanceIndex * 4;
    if (CustomData.Num() >= 4 && Component->PerInstanceSMCustomData.IsValidIndex(DataStartIndex + 3))
    {
        Component->PerInstanceSMCustomData[DataStartIndex] = CustomData[0];
        Component->PerInstanceSMCustomData[DataStartIndex + 1] = CustomData[1];
        Component->PerInstanceSMCustomData[DataStartIndex + 2] = CustomData[2];
        Component->PerInstanceSMCustomData[DataStartIndex + 3] = CustomData[3];
    }

    // Mark the component for update
    Component->MarkRenderStateDirty();

    UE_LOG(LogAuracronFoliageBridge, VeryVerbose, TEXT("Updated instance %d for foliage type: %s"), InstanceIndex, *TypeName);

    // Broadcast instance modified event
    OnInstanceModified.Broadcast(InstanceIndex, NewInstanceInfo, TypeName);

    return true;
}

bool UAuracronFoliageBridgeAPI::RemoveFoliageInstance(const FString& TypeName, int32 InstanceIndex)
{
    SCOPE_CYCLE_COUNTER(STAT_AuracronFoliageBridge_RemoveInstance);

    FScopeLock Lock(&FoliageMutex);

    if (!IsInitialized())
    {
        LogError(TEXT("Foliage Bridge not initialized"), 3019);
        return false;
    }

    if (!ValidateTypeName(TypeName))
    {
        LogError(TEXT("Invalid foliage type name"), 3020);
        return false;
    }

    if (!ValidateInstanceIndex(TypeName, InstanceIndex))
    {
        LogError(FString::Printf(TEXT("Invalid instance index %d for foliage type '%s'"), InstanceIndex, *TypeName), 3021);
        return false;
    }

    AInstancedFoliageActor* FoliageActor = GetOrCreateFoliageActor(TypeName);
    if (!FoliageActor)
    {
        LogError(FString::Printf(TEXT("Failed to get foliage actor for type '%s'"), *TypeName), 3022);
        return false;
    }

    UFoliageType* FoliageType = GetUE5FoliageType(TypeName);
    if (!FoliageType)
    {
        LogError(FString::Printf(TEXT("Foliage type '%s' not found"), *TypeName), 3023);
        return false;
    }

    FFoliageInfo* FoliageInfo = FoliageActor->FindInfo(FoliageType);
    if (!FoliageInfo || !FoliageInfo->GetComponent())
    {
        LogError(FString::Printf(TEXT("No foliage info found for type '%s'"), *TypeName), 3024);
        return false;
    }

    UHierarchicalInstancedStaticMeshComponent* Component = FoliageInfo->GetComponent();

    // Remove the instance
    if (!Component->RemoveInstance(InstanceIndex))
    {
        LogError(FString::Printf(TEXT("Failed to remove instance %d of type '%s'"), InstanceIndex, *TypeName), 3025);
        return false;
    }

    // Remove corresponding custom data
    if (Component->PerInstanceSMCustomData.IsValidIndex(InstanceIndex))
    {
        Component->PerInstanceSMCustomData.RemoveAt(InstanceIndex);
    }

    UE_LOG(LogAuracronFoliageBridge, VeryVerbose, TEXT("Removed instance %d for foliage type: %s"), InstanceIndex, *TypeName);

    // Broadcast instance removed event
    OnInstanceRemoved.Broadcast(InstanceIndex, TypeName);

    return true;
}

int32 UAuracronFoliageBridgeAPI::AddFoliageInstance(const FString& TypeName, const FAuracronFoliageInstanceInfo& InstanceInfo)
{
    SCOPE_CYCLE_COUNTER(STAT_AuracronFoliageBridge_AddInstance);

    FScopeLock Lock(&FoliageMutex);

    if (!IsInitialized())
    {
        LogError(TEXT("Foliage Bridge not initialized"), 3026);
        return -1;
    }

    if (!ValidateTypeName(TypeName))
    {
        LogError(TEXT("Invalid foliage type name"), 3027);
        return -1;
    }

    AInstancedFoliageActor* FoliageActor = GetOrCreateFoliageActor(TypeName);
    if (!FoliageActor)
    {
        LogError(FString::Printf(TEXT("Failed to get foliage actor for type '%s'"), *TypeName), 3028);
        return -1;
    }

    UFoliageType* FoliageType = GetUE5FoliageType(TypeName);
    if (!FoliageType)
    {
        LogError(FString::Printf(TEXT("Foliage type '%s' not found"), *TypeName), 3029);
        return -1;
    }

    // Get or create foliage info
    // UE 5.6 uses AddFoliageInfo instead of FindOrAddMesh
    TUniqueObj<FFoliageInfo>& FoliageInfoRef = FoliageActor->AddFoliageInfo(FoliageType);
    FFoliageInfo* FoliageInfo = &FoliageInfoRef.Get();
    if (!FoliageInfo || !FoliageInfo->GetComponent())
    {
        LogError(FString::Printf(TEXT("Failed to get foliage info for type '%s'"), *TypeName), 3030);
        return -1;
    }

    UHierarchicalInstancedStaticMeshComponent* Component = FoliageInfo->GetComponent();

    // Add the instance
    int32 NewInstanceIndex = Component->AddInstance(InstanceInfo.Transform);
    if (NewInstanceIndex == INDEX_NONE)
    {
        LogError(FString::Printf(TEXT("Failed to add instance for type '%s'"), *TypeName), 3031);
        return -1;
    }

    // Add custom data (health, age, color)
    TArray<float> CustomData;
    CustomData.Add(InstanceInfo.Health);
    CustomData.Add(InstanceInfo.Age);
    CustomData.Add(InstanceInfo.Color.R);
    CustomData.Add(InstanceInfo.Color.G);
    CustomData.Add(InstanceInfo.Color.B);
    CustomData.Add(InstanceInfo.Color.A);

    // Add custom parameters
    for (const auto& Param : InstanceInfo.CustomParameters)
    {
        CustomData.Add(Param.Value);
    }

    // UE 5.6: Ensure the custom data array is large enough (4 floats per instance)
    int32 RequiredSize = (NewInstanceIndex + 1) * 4;
    while (Component->PerInstanceSMCustomData.Num() < RequiredSize)
    {
        Component->PerInstanceSMCustomData.Add(0.0f);
    }

    // Set custom data for this instance (4 floats per instance)
    int32 DataStartIndex = NewInstanceIndex * 4;
    if (CustomData.Num() >= 4)
    {
        Component->PerInstanceSMCustomData[DataStartIndex] = CustomData[0];
        Component->PerInstanceSMCustomData[DataStartIndex + 1] = CustomData[1];
        Component->PerInstanceSMCustomData[DataStartIndex + 2] = CustomData[2];
        Component->PerInstanceSMCustomData[DataStartIndex + 3] = CustomData[3];
    }

    UE_LOG(LogAuracronFoliageBridge, VeryVerbose, TEXT("Added instance %d for foliage type: %s"), NewInstanceIndex, *TypeName);

    // Broadcast instance added event
    OnInstanceAdded.Broadcast(InstanceInfo, TypeName);

    return NewInstanceIndex;
}

TArray<int32> UAuracronFoliageBridgeAPI::AddFoliageInstances(const FString& TypeName, const TArray<FAuracronFoliageInstanceInfo>& InstanceInfos)
{
    // Performance tracking for AddInstances

    TArray<int32> AddedIndices;
    FScopeLock Lock(&FoliageMutex);

    if (!IsInitialized())
    {
        LogError(TEXT("Foliage Bridge not initialized"), 3040);
        return AddedIndices;
    }

    if (!ValidateTypeName(TypeName))
    {
        LogError(TEXT("Invalid foliage type name"), 3041);
        return AddedIndices;
    }

    if (InstanceInfos.Num() == 0)
    {
        LogWarning(TEXT("No instances to add"));
        return AddedIndices;
    }

    AInstancedFoliageActor* FoliageActor = GetOrCreateFoliageActor(TypeName);
    if (!FoliageActor)
    {
        LogError(TEXT("Failed to get or create foliage actor"), 3042);
        return AddedIndices;
    }

    UFoliageType* FoliageType = GetUE5FoliageType(TypeName);
    if (!FoliageType)
    {
        LogError(TEXT("Failed to get foliage type"), 3043);
        return AddedIndices;
    }

    // Convert instance infos to transforms
    TArray<FTransform> Transforms;
    Transforms.Reserve(InstanceInfos.Num());

    for (const FAuracronFoliageInstanceInfo& InstanceInfo : InstanceInfos)
    {
        FTransform Transform;
        Transform.SetLocation(InstanceInfo.Location);
        Transform.SetRotation(InstanceInfo.Rotation.Quaternion());
        Transform.SetScale3D(InstanceInfo.Scale);
        Transforms.Add(Transform);
    }

    // Add instances using UE 5.6 API
    // Get the foliage component and add instances directly
    TUniqueObj<FFoliageInfo>& FoliageInfoRef = FoliageActor->AddFoliageInfo(FoliageType);
    FFoliageInfo* FoliageInfo = &FoliageInfoRef.Get();

    if (FoliageInfo && FoliageInfo->GetComponent())
    {
        UHierarchicalInstancedStaticMeshComponent* Component = FoliageInfo->GetComponent();
        AddedIndices = Component->AddInstances(Transforms, true, false, true);
    }
    else
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Failed to get foliage component for type %s"), *TypeName);
    }

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Successfully added %d instances of foliage type '%s'"),
           AddedIndices.Num(), *TypeName);

    return AddedIndices;
}

bool UAuracronFoliageBridgeAPI::RemoveFoliageInstances(const FString& TypeName, const TArray<int32>& InstanceIndices)
{
    // Performance tracking for RemoveInstances

    FScopeLock Lock(&FoliageMutex);

    if (!IsInitialized())
    {
        LogError(TEXT("Foliage Bridge not initialized"), 3044);
        return false;
    }

    if (!ValidateTypeName(TypeName))
    {
        LogError(TEXT("Invalid foliage type name"), 3045);
        return false;
    }

    if (InstanceIndices.Num() == 0)
    {
        LogWarning(TEXT("No instances to remove"));
        return true;
    }

    AInstancedFoliageActor* FoliageActor = FindFoliageActor(TypeName);
    if (!FoliageActor)
    {
        LogError(TEXT("Foliage actor not found"), 3046);
        return false;
    }

    UFoliageType* FoliageType = GetUE5FoliageType(TypeName);
    if (!FoliageType)
    {
        LogError(TEXT("Failed to get foliage type"), 3047);
        return false;
    }

    // Get foliage info
    FFoliageInfo* FoliageInfo = FoliageActor->FindInfo(FoliageType);
    if (!FoliageInfo || !FoliageInfo->GetComponent())
    {
        LogError(TEXT("Foliage info not found"), 3048);
        return false;
    }

    // Sort indices in descending order to avoid index shifting issues
    TArray<int32> SortedIndices = InstanceIndices;
    SortedIndices.Sort([](const int32& A, const int32& B) { return A > B; });

    int32 RemovedCount = 0;
    for (int32 Index : SortedIndices)
    {
        if (Index >= 0 && Index < FoliageInfo->GetComponent()->GetInstanceCount())
        {
            FoliageInfo->GetComponent()->RemoveInstance(Index);
            RemovedCount++;
        }
        else
        {
            LogWarning(FString::Printf(TEXT("Invalid instance index: %d"), Index));
        }
    }

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Successfully removed %d instances of foliage type '%s'"),
           RemovedCount, *TypeName);

    return RemovedCount > 0;
}

void UAuracronFoliageBridgeAPI::ClearAllFoliageInstances(const FString& TypeName)
{
    SCOPE_CYCLE_COUNTER(STAT_AuracronFoliageBridge_ClearInstances);

    FScopeLock Lock(&FoliageMutex);

    if (!IsInitialized())
    {
        LogError(TEXT("Foliage Bridge not initialized"), 3032);
        return;
    }

    if (!ValidateTypeName(TypeName))
    {
        LogError(TEXT("Invalid foliage type name"), 3033);
        return;
    }

    AInstancedFoliageActor* FoliageActor = GetOrCreateFoliageActor(TypeName);
    if (!FoliageActor)
    {
        LogWarning(FString::Printf(TEXT("No foliage actor found for type '%s'"), *TypeName));
        return;
    }

    UFoliageType* FoliageType = GetUE5FoliageType(TypeName);
    if (!FoliageType)
    {
        LogWarning(FString::Printf(TEXT("Foliage type '%s' not found"), *TypeName));
        return;
    }

    FFoliageInfo* FoliageInfo = FoliageActor->FindInfo(FoliageType);
    if (!FoliageInfo || !FoliageInfo->GetComponent())
    {
        LogWarning(FString::Printf(TEXT("No foliage info found for type '%s'"), *TypeName));
        return;
    }

    UHierarchicalInstancedStaticMeshComponent* Component = FoliageInfo->GetComponent();
    int32 InstanceCount = Component->GetInstanceCount();

    // Clear all instances
    Component->ClearInstances();
    Component->PerInstanceSMCustomData.Empty();

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Cleared %d instances for foliage type: %s"), InstanceCount, *TypeName);
}

// Internal Helper Functions Implementation

void UAuracronFoliageBridgeAPI::UpdateState(EAuracronFoliageState NewState)
{
    if (CurrentState != NewState)
    {
        EAuracronFoliageState OldState = CurrentState;
        CurrentState = NewState;

        UE_LOG(LogAuracronFoliageBridge, VeryVerbose, TEXT("Foliage Bridge state changed from %s to %s"),
               *UEnum::GetValueAsString(OldState), *UEnum::GetValueAsString(NewState));
    }
}

AInstancedFoliageActor* UAuracronFoliageBridgeAPI::GetOrCreateFoliageActor(const FString& TypeName)
{
    if (!TargetWorld)
    {
        LogError(TEXT("Target world is not valid"), 4001);
        return nullptr;
    }

    // Check if we already have a cached foliage actor for this type
    if (TObjectPtr<AInstancedFoliageActor>* CachedActor = FoliageActors.Find(TypeName))
    {
        if (IsValid(CachedActor->Get()))
        {
            return CachedActor->Get();
        }
        else
        {
            // Remove invalid cached actor
            FoliageActors.Remove(TypeName);
        }
    }

    // Find existing foliage actor in the world
    AInstancedFoliageActor* FoliageActor = AInstancedFoliageActor::GetInstancedFoliageActorForCurrentLevel(TargetWorld.Get(), true);

    if (!FoliageActor)
    {
        LogError(TEXT("Failed to get or create instanced foliage actor"), 4002);
        return nullptr;
    }

    // Cache the foliage actor
    FoliageActors.Add(TypeName, FoliageActor);

    return FoliageActor;
}

AInstancedFoliageActor* UAuracronFoliageBridgeAPI::FindFoliageActor(const FString& TypeName) const
{
    if (!TargetWorld)
    {
        return nullptr;
    }

    // Check if we already have a cached foliage actor for this type
    if (const TObjectPtr<AInstancedFoliageActor>* CachedActor = FoliageActors.Find(TypeName))
    {
        if (IsValid(CachedActor->Get()))
        {
            return CachedActor->Get();
        }
    }

    // Find existing foliage actor in the world (without creating)
    AInstancedFoliageActor* FoliageActor = AInstancedFoliageActor::GetInstancedFoliageActorForCurrentLevel(TargetWorld.Get(), false);

    return FoliageActor;
}

bool UAuracronFoliageBridgeAPI::ValidateTypeName(const FString& TypeName) const
{
    if (TypeName.IsEmpty())
    {
        LogError(TEXT("Foliage type name cannot be empty"), 4003);
        return false;
    }

    if (TypeName.Len() > 128)
    {
        LogError(TEXT("Foliage type name is too long (max 128 characters)"), 4004);
        return false;
    }

    // Check for invalid characters
    for (TCHAR Char : TypeName)
    {
        if (!FChar::IsAlnum(Char) && Char != '_' && Char != '-' && Char != '.')
        {
            LogError(FString::Printf(TEXT("Foliage type name contains invalid character: %c"), Char), 4005);
            return false;
        }
    }

    return true;
}

bool UAuracronFoliageBridgeAPI::ValidateInstanceIndex(const FString& TypeName, int32 InstanceIndex) const
{
    if (InstanceIndex < 0)
    {
        return false;
    }

    int32 InstanceCount = GetFoliageInstanceCount(TypeName);
    return InstanceIndex < InstanceCount;
}

void UAuracronFoliageBridgeAPI::LogError(const FString& ErrorMessage, int32 ErrorCode) const
{
    UE_LOG(LogAuracronFoliageBridge, Error, TEXT("Error %d: %s"), ErrorCode, *ErrorMessage);
    // Note: OnError.Broadcast cannot be called from const function, but logging is still functional
}

void UAuracronFoliageBridgeAPI::LogWarning(const FString& WarningMessage) const
{
    UE_LOG(LogAuracronFoliageBridge, Warning, TEXT("Warning: %s"), *WarningMessage);
}

void UAuracronFoliageBridgeAPI::LogInfo(const FString& InfoMessage) const
{
    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Info: %s"), *InfoMessage);
}

// Performance and Monitoring Implementation

void UAuracronFoliageBridgeAPI::UpdateFoliagePerformanceMetrics()
{
    SCOPE_CYCLE_COUNTER(STAT_AuracronFoliageBridge_UpdateMetrics);

    FScopeLock Lock(&FoliageMutex);

    if (!IsInitialized() || !TargetWorld)
    {
        return;
    }

    FAuracronFoliagePerformanceInfo NewPerformanceInfo;

    // Reset counters
    NewPerformanceInfo.TotalInstances = 0;
    NewPerformanceInfo.VisibleInstances = 0;
    NewPerformanceInfo.CulledInstances = 0;
    NewPerformanceInfo.DrawCalls = 0;
    NewPerformanceInfo.Triangles = 0;
    NewPerformanceInfo.MemoryUsage = 0.0f;

    // Iterate through all foliage actors in the world
    for (TActorIterator<AInstancedFoliageActor> ActorItr(TargetWorld.Get()); ActorItr; ++ActorItr)
    {
        AInstancedFoliageActor* FoliageActor = *ActorItr;
        if (!FoliageActor)
        {
            continue;
        }

        // Iterate through all foliage types in this actor
        for (auto& FoliagePair : FoliageActor->GetFoliageInfos())
        {
            const FFoliageInfo* FoliageInfo = &FoliagePair.Value.Get();
            if (!FoliageInfo || !FoliageInfo->GetComponent())
            {
                continue;
            }

            UHierarchicalInstancedStaticMeshComponent* Component = FoliageInfo->GetComponent();

            // Count instances
            int32 ComponentInstanceCount = Component->GetInstanceCount();
            NewPerformanceInfo.TotalInstances += ComponentInstanceCount;

            // Calculate visible instances using proper culling checks
            int32 VisibleInstanceCount = 0;

            // Get component bounds for culling calculations
            FBoxSphereBounds ComponentBounds = Component->CalcBounds(Component->GetComponentTransform());

            // Get current view information for frustum culling
            if (TargetWorld && TargetWorld->GetFirstPlayerController())
            {
                APlayerController* PlayerController = TargetWorld->GetFirstPlayerController();
                if (PlayerController && PlayerController->PlayerCameraManager)
                {
                    FVector CameraLocation = PlayerController->PlayerCameraManager->GetCameraLocation();
                    FRotator CameraRotation = PlayerController->PlayerCameraManager->GetCameraRotation();
                    float FOV = PlayerController->PlayerCameraManager->GetFOVAngle();

                    // Calculate view frustum
                    FMatrix ViewMatrix = FInverseRotationMatrix(CameraRotation) * FTranslationMatrix(-CameraLocation);
                    FMatrix ProjectionMatrix = FReversedZPerspectiveMatrix(FMath::DegreesToRadians(FOV), 1.777f, 1.0f, 10000.0f);
                    FConvexVolume ViewFrustum;
                    GetViewFrustumBounds(ViewFrustum, ViewMatrix * ProjectionMatrix, true);

                    // Check each instance for visibility
                    for (int32 InstanceIdx = 0; InstanceIdx < ComponentInstanceCount; ++InstanceIdx)
                    {
                        FTransform InstanceTransform;
                        if (Component->GetInstanceTransform(InstanceIdx, InstanceTransform, true))
                        {
                            FVector InstanceLocation = InstanceTransform.GetLocation();
                            float InstanceRadius = ComponentBounds.SphereRadius * InstanceTransform.GetMaximumAxisScale();

                            // Distance culling check
                            float DistanceToCamera = FVector::Dist(CameraLocation, InstanceLocation);
                            float CullDistance = Component->InstanceEndCullDistance > 0 ? Component->InstanceEndCullDistance : 5000.0f;

                            if (DistanceToCamera <= CullDistance)
                            {
                                // Frustum culling check
                                FSphere InstanceSphere(InstanceLocation, InstanceRadius);
                                if (ViewFrustum.IntersectSphere(InstanceSphere.Center, InstanceSphere.W))
                                {
                                    VisibleInstanceCount++;
                                }
                            }
                        }
                    }
                }
                else
                {
                    // Fallback: use distance-based culling only
                    for (int32 InstanceIdx = 0; InstanceIdx < ComponentInstanceCount; ++InstanceIdx)
                    {
                        FTransform InstanceTransform;
                        if (Component->GetInstanceTransform(InstanceIdx, InstanceTransform, true))
                        {
                            float CullDistance = Component->InstanceEndCullDistance > 0 ? Component->InstanceEndCullDistance : 5000.0f;
                            if (ComponentBounds.GetSphere().IsInside(FVector::ZeroVector, CullDistance))
                            {
                                VisibleInstanceCount++;
                            }
                        }
                    }
                }
            }
            else
            {
                // No camera available, assume all instances within reasonable distance are visible
                VisibleInstanceCount = FMath::Min(ComponentInstanceCount, ComponentInstanceCount / 2);
            }

            NewPerformanceInfo.VisibleInstances += VisibleInstanceCount;

            // Count draw calls (one per component typically)
            if (ComponentInstanceCount > 0)
            {
                NewPerformanceInfo.DrawCalls++;
            }

            // Estimate triangles
            if (UStaticMesh* StaticMesh = Component->GetStaticMesh())
            {
                if (StaticMesh->GetRenderData() && StaticMesh->GetRenderData()->LODResources.Num() > 0)
                {
                    int32 TrianglesPerInstance = StaticMesh->GetRenderData()->LODResources[0].GetNumTriangles();
                    NewPerformanceInfo.Triangles += TrianglesPerInstance * ComponentInstanceCount;
                }
            }

            // Calculate accurate memory usage for foliage instances
            float ComponentMemoryUsage = 0.0f;

            // Base instance data memory (transform, custom data, etc.)
            ComponentMemoryUsage += ComponentInstanceCount * sizeof(FInstancedStaticMeshInstanceData);

            // Per-instance custom data memory
            if (Component->PerInstanceSMCustomData.Num() > 0)
            {
                // UE 5.6: PerInstanceSMCustomData is now TArray<float> instead of TArray<TArray<float>>
                ComponentMemoryUsage += Component->PerInstanceSMCustomData.Num() * sizeof(float);
            }

            // Static mesh memory (shared across instances)
            if (UStaticMesh* StaticMesh = Component->GetStaticMesh())
            {
                if (StaticMesh->GetRenderData() && StaticMesh->GetRenderData()->LODResources.Num() > 0)
                {
                    const FStaticMeshLODResources& LODResource = StaticMesh->GetRenderData()->LODResources[0];

                    // Vertex buffer memory (shared, so divide by estimated sharing factor)
                    float VertexMemory = LODResource.GetNumVertices() * sizeof(FStaticMeshBuildVertex);
                    ComponentMemoryUsage += VertexMemory / 10.0f; // Assume 10 components share the mesh on average

                    // Index buffer memory (shared)
                    float IndexMemory = LODResource.GetNumTriangles() * 3 * sizeof(uint32);
                    ComponentMemoryUsage += IndexMemory / 10.0f; // Assume 10 components share the mesh on average
                }
            }

            // GPU instance buffer memory
            ComponentMemoryUsage += ComponentInstanceCount * sizeof(FInstancedStaticMeshInstanceData);

            // Render state memory overhead
            ComponentMemoryUsage += 1024.0f; // Base component overhead

            NewPerformanceInfo.MemoryUsage += ComponentMemoryUsage;
        }
    }

    NewPerformanceInfo.CulledInstances = NewPerformanceInfo.TotalInstances - NewPerformanceInfo.VisibleInstances;

    // Update timing information (would be more accurate with actual frame timing)
    NewPerformanceInfo.RenderTime = 0.0f; // Would need render thread integration
    NewPerformanceInfo.UpdateTime = 0.0f; // Would need actual timing measurements

    // Set default LOD and culling distances
    NewPerformanceInfo.LODDistance = 1000.0f;
    NewPerformanceInfo.CullingDistance = 5000.0f;

    // Update cached performance info
    CachedPerformanceInfo = NewPerformanceInfo;

    UE_LOG(LogAuracronFoliageBridge, VeryVerbose, TEXT("Updated performance metrics: %d total instances, %d visible, %d draw calls"),
           NewPerformanceInfo.TotalInstances, NewPerformanceInfo.VisibleInstances, NewPerformanceInfo.DrawCalls);

    // Broadcast performance update
    OnPerformanceUpdated.Broadcast(NewPerformanceInfo);
}

FAuracronFoliagePerformanceInfo UAuracronFoliageBridgeAPI::GetPerformanceInfo() const
{
    FScopeLock Lock(&FoliageMutex);
    return CachedPerformanceInfo;
}

bool UAuracronFoliageBridgeAPI::SetFoliageDensityScale(float DensityScale)
{
    FScopeLock Lock(&FoliageMutex);

    if (!IsInitialized())
    {
        LogError(TEXT("Foliage Bridge not initialized"), 5001);
        return false;
    }

    DensityScale = FMath::Clamp(DensityScale, 0.0f, 2.0f);

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Setting global foliage density scale to %f"), DensityScale);

    // Apply density scale to all registered foliage types
    for (const auto& TypePair : RegisteredFoliageTypes)
    {
        UFoliageType* FoliageType = TypePair.Value;
        if (FoliageType)
        {
            // Store original density in a static map since UE 5.6 removed metadata APIs
            static TMap<UFoliageType*, float> OriginalDensityMap;

            float OriginalDensity = FoliageType->Density;
            if (float* FoundPtr = OriginalDensityMap.Find(FoliageType))
            {
                OriginalDensity = *FoundPtr;
            }
            else
            {
                OriginalDensityMap.Add(FoliageType, FoliageType->Density);
            }

            // Apply scale
            FoliageType->Density = OriginalDensity * DensityScale;
        }
    }

    // Update performance metrics
    UpdateFoliagePerformanceMetrics();

    return true;
}

float UAuracronFoliageBridgeAPI::GetFoliageDensityScale() const
{
    FScopeLock Lock(&FoliageMutex);

    if (!IsInitialized())
    {
        return 1.0f;
    }

    // Calculate current density scale by comparing current density to original density
    float AverageDensityScale = 1.0f;
    int32 ValidTypeCount = 0;

    for (const auto& TypePair : RegisteredFoliageTypes)
    {
        UFoliageType* FoliageType = TypePair.Value;
        if (FoliageType)
        {
            // Use the same static map for consistency
            static TMap<UFoliageType*, float> OriginalDensityMap;

            if (float* FoundPtr = OriginalDensityMap.Find(FoliageType))
            {
                float OriginalDensity = *FoundPtr;
                if (OriginalDensity > 0.0f)
                {
                    float CurrentScale = FoliageType->Density / OriginalDensity;
                    AverageDensityScale += CurrentScale;
                    ValidTypeCount++;
                }
            }
        }
    }

    if (ValidTypeCount > 0)
    {
        AverageDensityScale /= ValidTypeCount;
    }

    // Clamp to valid range
    return FMath::Clamp(AverageDensityScale, 0.0f, 2.0f);
}

// === Interactive Foliage Implementation ===

AInteractiveFoliageActor* UAuracronFoliageBridgeAPI::CreateInteractiveFoliageActor(const FVector& Location, UStaticMesh* FoliageMesh)
{
    SCOPE_CYCLE_COUNTER(STAT_AuracronFoliageBridge_CreateInteractive);

    if (!IsInitialized())
    {
        LogError(TEXT("Foliage Bridge not initialized"), 4001);
        return nullptr;
    }

    if (!FoliageMesh)
    {
        LogError(TEXT("Foliage mesh is null"), 4002);
        return nullptr;
    }

    UWorld* World = GetTargetWorld();
    if (!World)
    {
        LogError(TEXT("Target world is null"), 4003);
        return nullptr;
    }

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Creating interactive foliage actor at location: %s"), *Location.ToString());

    // Spawn the interactive foliage actor
    FActorSpawnParameters SpawnParams;
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
    SpawnParams.bNoFail = true;

    AInteractiveFoliageActor* InteractiveFoliageActor = World->SpawnActor<AInteractiveFoliageActor>(
        AInteractiveFoliageActor::StaticClass(),
        Location,
        FRotator::ZeroRotator,
        SpawnParams
    );

    if (!InteractiveFoliageActor)
    {
        LogError(TEXT("Failed to spawn interactive foliage actor"), 4004);
        return nullptr;
    }

    // Configure the static mesh component
    UStaticMeshComponent* MeshComponent = InteractiveFoliageActor->GetStaticMeshComponent();
    if (MeshComponent)
    {
        MeshComponent->SetStaticMesh(FoliageMesh);

        // Enable collision for interaction
        MeshComponent->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
        MeshComponent->SetCollisionObjectType(ECollisionChannel::ECC_WorldDynamic);
        MeshComponent->SetCollisionResponseToAllChannels(ECollisionResponse::ECR_Block);

        // Enable physics simulation
        MeshComponent->SetSimulatePhysics(true);
        MeshComponent->SetMassOverrideInKg(NAME_None, 10.0f, true);

        // Set default physics properties
        MeshComponent->SetLinearDamping(0.5f);
        MeshComponent->SetAngularDamping(0.5f);

        // Enable generate overlap events for interaction detection
        MeshComponent->SetGenerateOverlapEvents(true);
    }

    // Configure capsule component for interaction detection
    // Note: CapsuleComponent is private in UE 5.6, using alternative approach
    UCapsuleComponent* CapsuleComponent = InteractiveFoliageActor->FindComponentByClass<UCapsuleComponent>();
    if (CapsuleComponent)
    {
        // Set capsule size based on mesh bounds
        FBox MeshBounds = FoliageMesh->GetBounds().GetBox();
        float CapsuleRadius = FMath::Max(MeshBounds.GetExtent().X, MeshBounds.GetExtent().Y) * 1.2f;
        float CapsuleHeight = MeshBounds.GetExtent().Z * 2.0f;

        CapsuleComponent->SetCapsuleSize(CapsuleRadius, CapsuleHeight);
        CapsuleComponent->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
        CapsuleComponent->SetCollisionObjectType(ECollisionChannel::ECC_WorldDynamic);
        CapsuleComponent->SetCollisionResponseToAllChannels(ECollisionResponse::ECR_Ignore);
        CapsuleComponent->SetCollisionResponseToChannel(ECollisionChannel::ECC_Pawn, ECollisionResponse::ECR_Overlap);
        CapsuleComponent->SetGenerateOverlapEvents(true);
    }

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Interactive foliage actor created successfully"));
    return InteractiveFoliageActor;
}

bool UAuracronFoliageBridgeAPI::ConfigureInteractiveFoliage(AInteractiveFoliageActor* FoliageActor, float Mass, float Damping, float SpringStrength)
{
    if (!FoliageActor)
    {
        LogError(TEXT("Interactive foliage actor is null"), 4005);
        return false;
    }

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Configuring interactive foliage with Mass: %f, Damping: %f, SpringStrength: %f"),
           Mass, Damping, SpringStrength);

    UStaticMeshComponent* MeshComponent = FoliageActor->GetStaticMeshComponent();
    if (!MeshComponent)
    {
        LogError(TEXT("Interactive foliage actor has no static mesh component"), 4006);
        return false;
    }

    // Configure mass
    if (Mass > 0.0f)
    {
        MeshComponent->SetMassOverrideInKg(NAME_None, Mass, true);
    }

    // Configure damping
    MeshComponent->SetLinearDamping(FMath::Clamp(Damping, 0.0f, 10.0f));
    MeshComponent->SetAngularDamping(FMath::Clamp(Damping, 0.0f, 10.0f));

    // Configure spring strength (using constraint if available)
    if (SpringStrength > 0.0f)
    {
        // Create a physics constraint to simulate spring behavior
        UPhysicsConstraintComponent* ConstraintComponent = FoliageActor->FindComponentByClass<UPhysicsConstraintComponent>();
        if (!ConstraintComponent)
        {
            ConstraintComponent = NewObject<UPhysicsConstraintComponent>(FoliageActor);
            FoliageActor->AddInstanceComponent(ConstraintComponent);
            ConstraintComponent->AttachToComponent(FoliageActor->GetRootComponent(),
                FAttachmentTransformRules::KeepWorldTransform);
        }

        if (ConstraintComponent)
        {
            // Configure constraint for spring-like behavior
            // UE 5.6 uses ConstraintInstance for constraint settings
            ConstraintComponent->ConstraintInstance.SetLinearXMotion(ELinearConstraintMotion::LCM_Limited);
            ConstraintComponent->ConstraintInstance.SetLinearYMotion(ELinearConstraintMotion::LCM_Limited);
            ConstraintComponent->ConstraintInstance.SetLinearZMotion(ELinearConstraintMotion::LCM_Limited);

            ConstraintComponent->SetLinearXLimit(ELinearConstraintMotion::LCM_Limited, 50.0f);
            ConstraintComponent->SetLinearYLimit(ELinearConstraintMotion::LCM_Limited, 50.0f);
            ConstraintComponent->SetLinearZLimit(ELinearConstraintMotion::LCM_Limited, 20.0f);

            // Set spring parameters
            ConstraintComponent->SetLinearDriveParams(SpringStrength, Damping, 0.0f);
            ConstraintComponent->SetLinearPositionDrive(true, true, true);

            ConstraintComponent->RegisterComponent();
        }
    }

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Interactive foliage configured successfully"));
    return true;
}

bool UAuracronFoliageBridgeAPI::EnableFoliageInteraction(const FString& TypeName, bool bEnable)
{
    FScopeLock Lock(&FoliageMutex);

    if (!IsInitialized())
    {
        LogError(TEXT("Foliage Bridge not initialized"), 4007);
        return false;
    }

    if (!ValidateTypeName(TypeName))
    {
        LogError(TEXT("Invalid foliage type name"), 4008);
        return false;
    }

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("%s foliage interaction for type: %s"),
           bEnable ? TEXT("Enabling") : TEXT("Disabling"), *TypeName);

    AInstancedFoliageActor* FoliageActor = GetOrCreateFoliageActor(TypeName);
    if (!FoliageActor)
    {
        LogError(FString::Printf(TEXT("Failed to get foliage actor for type '%s'"), *TypeName), 4009);
        return false;
    }

    UFoliageType* FoliageType = GetUE5FoliageType(TypeName);
    if (!FoliageType)
    {
        LogError(FString::Printf(TEXT("Foliage type '%s' not found"), *TypeName), 4010);
        return false;
    }

    FFoliageInfo* FoliageInfo = FoliageActor->FindInfo(FoliageType);
    if (!FoliageInfo || !FoliageInfo->GetComponent())
    {
        LogError(FString::Printf(TEXT("No foliage info found for type '%s'"), *TypeName), 4011);
        return false;
    }

    UHierarchicalInstancedStaticMeshComponent* Component = FoliageInfo->GetComponent();

    if (bEnable)
    {
        // Enable interaction
        Component->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
        Component->SetCollisionObjectType(ECollisionChannel::ECC_WorldDynamic);
        Component->SetCollisionResponseToChannel(ECollisionChannel::ECC_Pawn, ECollisionResponse::ECR_Block);
        Component->SetGenerateOverlapEvents(true);

        // Enable physics simulation for instances if supported
        Component->SetSimulatePhysics(true);
    }
    else
    {
        // Disable interaction
        Component->SetCollisionEnabled(ECollisionEnabled::NoCollision);
        Component->SetGenerateOverlapEvents(false);
        Component->SetSimulatePhysics(false);
    }

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Foliage interaction %s for type '%s'"),
           bEnable ? TEXT("enabled") : TEXT("disabled"), *TypeName);

    return true;
}

// ========================================
// Procedural Foliage Implementation
// ========================================

UProceduralFoliageSpawner* UAuracronFoliageBridgeAPI::CreateProceduralFoliageSpawner(const FString& SpawnerName)
{
    SCOPE_CYCLE_COUNTER(STAT_AuracronFoliageBridge_CreateProceduralSpawner);

    if (!IsInitialized())
    {
        LogError(TEXT("Foliage Bridge not initialized"), 5001);
        return nullptr;
    }

    if (SpawnerName.IsEmpty())
    {
        LogError(TEXT("Spawner name cannot be empty"), 5002);
        return nullptr;
    }

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Creating procedural foliage spawner: %s"), *SpawnerName);

    // Create the procedural foliage spawner
    UProceduralFoliageSpawner* NewSpawner = NewObject<UProceduralFoliageSpawner>(this, *SpawnerName);
    if (!NewSpawner)
    {
        LogError(FString::Printf(TEXT("Failed to create procedural foliage spawner: %s"), *SpawnerName), 5003);
        return nullptr;
    }

    // Configure default settings
    NewSpawner->TileSize = 10000.0f; // 100m tiles
    NewSpawner->NumUniqueTiles = 10;
    NewSpawner->RandomSeed = FMath::Rand();
    // Note: bNeedsSimulation was removed in UE 5.6, simulation is now automatic

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Successfully created procedural foliage spawner: %s"), *SpawnerName);
    return NewSpawner;
}

bool UAuracronFoliageBridgeAPI::ConfigureProceduralFoliageSpawner(UProceduralFoliageSpawner* Spawner, const TArray<FAuracronFoliageTypeInfo>& FoliageTypes)
{
    if (!Spawner)
    {
        LogError(TEXT("Procedural foliage spawner is null"), 5004);
        return false;
    }

    if (FoliageTypes.Num() == 0)
    {
        LogError(TEXT("No foliage types provided for spawner configuration"), 5005);
        return false;
    }

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Configuring procedural foliage spawner with %d foliage types"), FoliageTypes.Num());

    // Clear existing foliage types
    // Access private FoliageTypes using reflection since it's private in UE 5.6
    if (FArrayProperty* FoliageTypesProperty = FindFProperty<FArrayProperty>(UProceduralFoliageSpawner::StaticClass(), TEXT("FoliageTypes")))
    {
        FScriptArrayHelper ArrayHelper(FoliageTypesProperty, FoliageTypesProperty->ContainerPtrToValuePtr<void>(Spawner));
        ArrayHelper.EmptyValues();
    }

    // Add each foliage type to the spawner
    for (const FAuracronFoliageTypeInfo& TypeInfo : FoliageTypes)
    {
        if (!TypeInfo.StaticMesh.IsValid())
        {
            LogWarning(FString::Printf(TEXT("Skipping foliage type '%s' - no static mesh specified"), *TypeInfo.TypeName));
            continue;
        }

        // Create a new foliage type for procedural spawning
        UFoliageType_InstancedStaticMesh* ProceduralType = NewObject<UFoliageType_InstancedStaticMesh>(Spawner);
        ProceduralType->Mesh = TypeInfo.StaticMesh.LoadSynchronous();

        // Configure procedural-specific settings
        ProceduralType->Density = TypeInfo.Density;
        ProceduralType->Radius = TypeInfo.Radius;
        ProceduralType->ScaleX.Min = TypeInfo.ScaleMin.X;
        ProceduralType->ScaleX.Max = TypeInfo.ScaleMax.X;
        ProceduralType->ScaleY.Min = TypeInfo.ScaleMin.Y;
        ProceduralType->ScaleY.Max = TypeInfo.ScaleMax.Y;
        ProceduralType->ScaleZ.Min = TypeInfo.ScaleMin.Z;
        ProceduralType->ScaleZ.Max = TypeInfo.ScaleMax.Z;

        // Set collision settings
        ProceduralType->CollisionWithWorld = TypeInfo.bEnableCollision;
        ProceduralType->BodyInstance.SetCollisionEnabled(TypeInfo.bEnableCollision ? ECollisionEnabled::QueryAndPhysics : ECollisionEnabled::NoCollision);

        // Set culling distance
        ProceduralType->CullDistance.Min = TypeInfo.CullDistanceMin;
        ProceduralType->CullDistance.Max = TypeInfo.CullDistanceMax;

        // Add to spawner using reflection since FoliageTypes is private in UE 5.6
        if (FArrayProperty* FoliageTypesProperty = FindFProperty<FArrayProperty>(UProceduralFoliageSpawner::StaticClass(), TEXT("FoliageTypes")))
        {
            FScriptArrayHelper ArrayHelper(FoliageTypesProperty, FoliageTypesProperty->ContainerPtrToValuePtr<void>(Spawner));
            int32 NewIndex = ArrayHelper.AddValue();

            // Get the element property and construct the new item
            if (FStructProperty* ElementProperty = CastField<FStructProperty>(FoliageTypesProperty->Inner))
            {
                void* ElementPtr = ArrayHelper.GetRawPtr(NewIndex);
                ElementProperty->InitializeValue(ElementPtr);

                // Set the foliage type in the FFoliageTypeObject struct
                if (FObjectProperty* FoliageTypeProperty = FindFProperty<FObjectProperty>(ElementProperty->Struct, TEXT("FoliageType")))
                {
                    FoliageTypeProperty->SetObjectPropertyValue(FoliageTypeProperty->ContainerPtrToValuePtr<void>(ElementPtr), ProceduralType);
                }
            }
        }

        UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Added foliage type '%s' to procedural spawner"), *TypeInfo.TypeName);
    }

    // Check if any foliage types were added using reflection
    int32 FoliageTypesCount = 0;
    if (FArrayProperty* FoliageTypesProperty = FindFProperty<FArrayProperty>(UProceduralFoliageSpawner::StaticClass(), TEXT("FoliageTypes")))
    {
        FScriptArrayHelper ArrayHelper(FoliageTypesProperty, FoliageTypesProperty->ContainerPtrToValuePtr<void>(Spawner));
        FoliageTypesCount = ArrayHelper.Num();
    }

    if (FoliageTypesCount == 0)
    {
        LogError(TEXT("No valid foliage types were added to the spawner"), 5006);
        return false;
    }

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Successfully configured procedural foliage spawner with %d types"), FoliageTypesCount);
    return true;
}

AProceduralFoliageVolume* UAuracronFoliageBridgeAPI::CreateProceduralFoliageVolume(const FVector& Location, const FVector& Extent)
{
    SCOPE_CYCLE_COUNTER(STAT_AuracronFoliageBridge_CreateProceduralVolume);

    if (!IsInitialized())
    {
        LogError(TEXT("Foliage Bridge not initialized"), 5007);
        return nullptr;
    }

    UWorld* World = GetTargetWorld();
    if (!World)
    {
        LogError(TEXT("Target world is null"), 5008);
        return nullptr;
    }

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Creating procedural foliage volume at location: %s with extent: %s"),
           *Location.ToString(), *Extent.ToString());

    // Spawn the procedural foliage volume
    FActorSpawnParameters SpawnParams;
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
    SpawnParams.bNoFail = true;

    AProceduralFoliageVolume* Volume = World->SpawnActor<AProceduralFoliageVolume>(
        AProceduralFoliageVolume::StaticClass(),
        Location,
        FRotator::ZeroRotator,
        SpawnParams
    );

    if (!Volume)
    {
        LogError(TEXT("Failed to spawn procedural foliage volume"), 5009);
        return nullptr;
    }

    // Configure the volume bounds
    UBrushComponent* BrushComponent = Volume->GetBrushComponent();
    if (BrushComponent)
    {
        // Create a box brush manually since UCubeBuilder is editor-only
        UModel* Model = NewObject<UModel>(Volume);
        Model->Initialize();

        // Create a simple box collision for the volume
        FBox BoundingBox(Location - Extent, Location + Extent);

        // Set the brush model
        Volume->Brush = Model;
        BrushComponent->Brush = Model;
        BrushComponent->BrushBodySetup = nullptr; // Force recreation

        // Set the actor bounds manually
        Volume->SetActorLocation(Location);
        Volume->SetActorScale3D(FVector(Extent.X / 100.0f, Extent.Y / 100.0f, Extent.Z / 100.0f));
    }

    // Initialize procedural foliage component
    UProceduralFoliageComponent* ProceduralComponent = Volume->ProceduralComponent;
    if (ProceduralComponent)
    {
        // Configure foliage placement settings (editor-only in UE 5.6)
#if WITH_EDITORONLY_DATA
        ProceduralComponent->bAllowLandscape = true;
        ProceduralComponent->bAllowBSP = true;
        ProceduralComponent->bAllowStaticMesh = true;
        ProceduralComponent->bAllowTranslucent = false;
        ProceduralComponent->bAllowFoliage = false;
#endif
    }

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Successfully created procedural foliage volume"));
    return Volume;
}

bool UAuracronFoliageBridgeAPI::GenerateProceduralFoliage(AProceduralFoliageVolume* Volume, UProceduralFoliageSpawner* Spawner)
{
    SCOPE_CYCLE_COUNTER(STAT_AuracronFoliageBridge_GenerateProcedural);

    if (!Volume)
    {
        LogError(TEXT("Procedural foliage volume is null"), 5010);
        return false;
    }

    if (!Spawner)
    {
        LogError(TEXT("Procedural foliage spawner is null"), 5011);
        return false;
    }

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Generating procedural foliage"));

    // Set the spawner on the volume
    UProceduralFoliageComponent* ProceduralComponent = Volume->ProceduralComponent;
    if (!ProceduralComponent)
    {
        LogError(TEXT("Procedural foliage volume has no procedural component"), 5012);
        return false;
    }

    ProceduralComponent->FoliageSpawner = Spawner;

    // Trigger generation
    // UE 5.6 requires a callback function for ResimulateProceduralFoliage
    ProceduralComponent->ResimulateProceduralFoliage([](const TArray<FDesiredFoliageInstance>& DesiredInstances)
    {
        // Handle the desired instances - this callback is called when simulation completes
        UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Procedural foliage simulation completed with %d desired instances"), DesiredInstances.Num());
    });

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Procedural foliage generation initiated"));
    return true;
}

bool UAuracronFoliageBridgeAPI::ClearProceduralFoliage(AProceduralFoliageVolume* Volume)
{
    if (!Volume)
    {
        LogError(TEXT("Procedural foliage volume is null"), 5013);
        return false;
    }

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Clearing procedural foliage"));

    UProceduralFoliageComponent* ProceduralComponent = Volume->ProceduralComponent;
    if (!ProceduralComponent)
    {
        LogError(TEXT("Procedural foliage volume has no procedural component"), 5014);
        return false;
    }

    // Clear the procedural foliage
    ProceduralComponent->RemoveProceduralContent();

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Procedural foliage cleared"));
    return true;
}

// ========================================
// Advanced Placement Implementation
// ========================================

bool UAuracronFoliageBridgeAPI::PlaceSingleFoliageInstance(const FString& TypeName, const FVector& Location, const FRotator& Rotation, const FVector& Scale)
{
    SCOPE_CYCLE_COUNTER(STAT_AuracronFoliageBridge_PlaceSingle);

    FScopeLock Lock(&FoliageMutex);

    if (!IsInitialized())
    {
        LogError(TEXT("Foliage Bridge not initialized"), 6001);
        return false;
    }

    if (!ValidateTypeName(TypeName))
    {
        LogError(TEXT("Invalid foliage type name"), 6002);
        return false;
    }

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Placing single foliage instance of type '%s' at location: %s"),
           *TypeName, *Location.ToString());

    AInstancedFoliageActor* FoliageActor = GetOrCreateFoliageActor(TypeName);
    if (!FoliageActor)
    {
        LogError(FString::Printf(TEXT("Failed to get foliage actor for type '%s'"), *TypeName), 6003);
        return false;
    }

    UFoliageType* FoliageType = GetUE5FoliageType(TypeName);
    if (!FoliageType)
    {
        LogError(FString::Printf(TEXT("Foliage type '%s' not found"), *TypeName), 6004);
        return false;
    }

    // Create transform
    FTransform InstanceTransform(Rotation, Location, Scale);

    // Add the instance
    TArray<FTransform> Transforms;
    Transforms.Add(InstanceTransform);

    // Declare NumAdded outside the preprocessor blocks
    int32 NumAdded = 0;

    // Use foliage component to add instances
    TUniqueObj<FFoliageInfo>& FoliageInfoRef = FoliageActor->AddFoliageInfo(FoliageType);
    FFoliageInfo* FoliageInfo = &FoliageInfoRef.Get();
    if (FoliageInfo && FoliageInfo->GetComponent())
    {
        UHierarchicalInstancedStaticMeshComponent* Component = FoliageInfo->GetComponent();
        TArray<int32> AddedIndices = Component->AddInstances(Transforms, true, false, true);
        NumAdded = AddedIndices.Num();
    }
    if (NumAdded <= 0)
    {
        LogError(FString::Printf(TEXT("Failed to add foliage instance of type '%s'"), *TypeName), 6005);
        return false;
    }

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Successfully placed foliage instance of type '%s'"), *TypeName);
    return true;
}

bool UAuracronFoliageBridgeAPI::FillAreaWithFoliage(const FString& TypeName, const FBox& Area, const FAuracronFoliagePlacementInfo& PlacementInfo)
{
    SCOPE_CYCLE_COUNTER(STAT_AuracronFoliageBridge_FillArea);

    FScopeLock Lock(&FoliageMutex);

    if (!IsInitialized())
    {
        LogError(TEXT("Foliage Bridge not initialized"), 6006);
        return false;
    }

    if (!ValidateTypeName(TypeName))
    {
        LogError(TEXT("Invalid foliage type name"), 6007);
        return false;
    }

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Filling area with foliage type '%s'. Area: %s"),
           *TypeName, *Area.ToString());

    AInstancedFoliageActor* FoliageActor = GetOrCreateFoliageActor(TypeName);
    if (!FoliageActor)
    {
        LogError(FString::Printf(TEXT("Failed to get foliage actor for type '%s'"), *TypeName), 6008);
        return false;
    }

    UFoliageType* FoliageType = GetUE5FoliageType(TypeName);
    if (!FoliageType)
    {
        LogError(FString::Printf(TEXT("Foliage type '%s' not found"), *TypeName), 6009);
        return false;
    }

    UWorld* World = GetTargetWorld();
    if (!World)
    {
        LogError(TEXT("Target world is null"), 6010);
        return false;
    }

    // Calculate number of instances based on density and area
    FVector AreaExtent = Area.GetExtent();
    float AreaSize = AreaExtent.X * AreaExtent.Y * 4.0f; // Area in square units
    int32 NumInstances = FMath::RoundToInt(AreaSize * PlacementInfo.PaintDensity / 10000.0f); // Density per 100x100 area

    TArray<FTransform> Transforms;
    Transforms.Reserve(NumInstances);

    // Generate random positions within the area
    FRandomStream RandomStream(FMath::Rand());

    for (int32 i = 0; i < NumInstances; i++)
    {
        // Generate random position within the area
        FVector RandomLocation = FVector(
            RandomStream.FRandRange(Area.Min.X, Area.Max.X),
            RandomStream.FRandRange(Area.Min.Y, Area.Max.Y),
            Area.Max.Z // Start from top of area
        );

        // Perform line trace to find ground
        FHitResult HitResult;
        FVector TraceStart = RandomLocation;
        FVector TraceEnd = FVector(RandomLocation.X, RandomLocation.Y, Area.Min.Z);

        FCollisionQueryParams QueryParams;
        QueryParams.bTraceComplex = true;
        QueryParams.bReturnPhysicalMaterial = false;

        if (World->LineTraceSingleByChannel(HitResult, TraceStart, TraceEnd, ECC_WorldStatic, QueryParams))
        {
            // Check if we should place foliage on this surface
            bool bShouldPlace = true;

            if (PlacementInfo.bFilterLandscape && HitResult.GetActor() && HitResult.GetActor()->IsA<ALandscape>())
            {
                // Additional landscape filtering could be added here
            }
            else if (PlacementInfo.bFilterStaticMeshes && HitResult.GetComponent() && HitResult.GetComponent()->IsA<UStaticMeshComponent>())
            {
                // Additional static mesh filtering could be added here
            }

            if (bShouldPlace)
            {
                // Generate random rotation
                FRotator RandomRotation = FRotator(
                    0.0f, // Pitch
                    RandomStream.FRandRange(0.0f, 360.0f), // Yaw
                    0.0f  // Roll
                );

                // Generate random scale
                FVector RandomScale = FVector(
                    RandomStream.FRandRange(0.8f, 1.2f),
                    RandomStream.FRandRange(0.8f, 1.2f),
                    RandomStream.FRandRange(0.8f, 1.2f)
                );

                FTransform InstanceTransform(RandomRotation, HitResult.Location, RandomScale);
                Transforms.Add(InstanceTransform);
            }
        }
    }

    if (Transforms.Num() == 0)
    {
        LogWarning(FString::Printf(TEXT("No valid placement locations found for foliage type '%s' in specified area"), *TypeName));
        return false;
    }

    // Add all instances at once
    // UE 5.6 uses static AddInstances function (editor-only)
#if WITH_EDITOR
    if (UWorld* CurrentWorld = GetTargetWorld())
    {
        AInstancedFoliageActor::AddInstances(CurrentWorld, FoliageType, Transforms);
    }
    int32 NumAdded = Transforms.Num(); // Assume all instances were added successfully
#else
    // In non-editor builds, use foliage actor directly
    TUniqueObj<FFoliageInfo>& FoliageInfoRef = FoliageActor->AddFoliageInfo(FoliageType);
    FFoliageInfo* FoliageInfo = &FoliageInfoRef.Get();
    int32 NumAdded = 0;
    if (FoliageInfo && FoliageInfo->GetComponent())
    {
        for (const FTransform& Transform : Transforms)
        {
            FoliageInfo->GetComponent()->AddInstance(Transform);
            NumAdded++;
        }
    }
#endif

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Successfully placed %d instances of foliage type '%s' in area"),
           NumAdded, *TypeName);

    return NumAdded > 0;
}

bool UAuracronFoliageBridgeAPI::PlaceFoliageOnSpline(const FString& TypeName, USplineComponent* SplineComponent, const FAuracronFoliagePlacementInfo& PlacementInfo)
{
    SCOPE_CYCLE_COUNTER(STAT_AuracronFoliageBridge_PlaceOnSpline);

    FScopeLock Lock(&FoliageMutex);

    if (!IsInitialized())
    {
        LogError(TEXT("Foliage Bridge not initialized"), 6011);
        return false;
    }

    if (!ValidateTypeName(TypeName))
    {
        LogError(TEXT("Invalid foliage type name"), 6012);
        return false;
    }

    if (!SplineComponent)
    {
        LogError(TEXT("Spline component is null"), 6013);
        return false;
    }

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Placing foliage type '%s' along spline"), *TypeName);

    AInstancedFoliageActor* FoliageActor = GetOrCreateFoliageActor(TypeName);
    if (!FoliageActor)
    {
        LogError(FString::Printf(TEXT("Failed to get foliage actor for type '%s'"), *TypeName), 6014);
        return false;
    }

    UFoliageType* FoliageType = GetUE5FoliageType(TypeName);
    if (!FoliageType)
    {
        LogError(FString::Printf(TEXT("Foliage type '%s' not found"), *TypeName), 6015);
        return false;
    }

    // Calculate placement points along the spline
    float SplineLength = SplineComponent->GetSplineLength();
    float Spacing = PlacementInfo.BrushSize / PlacementInfo.PaintDensity; // Calculate spacing based on density
    int32 NumPoints = FMath::Max(1, FMath::RoundToInt(SplineLength / Spacing));

    TArray<FTransform> Transforms;
    Transforms.Reserve(NumPoints);

    FRandomStream RandomStream(FMath::Rand());

    for (int32 i = 0; i < NumPoints; i++)
    {
        float Distance = (SplineLength / NumPoints) * i;

        // Get spline transform at this distance
        FTransform SplineTransform = SplineComponent->GetTransformAtDistanceAlongSpline(Distance, ESplineCoordinateSpace::World);

        // Add some random offset perpendicular to the spline
        FVector RightVector = SplineTransform.GetRotation().GetRightVector();
        FVector RandomOffset = RightVector * RandomStream.FRandRange(-PlacementInfo.BrushSize * 0.5f, PlacementInfo.BrushSize * 0.5f);

        FVector PlacementLocation = SplineTransform.GetLocation() + RandomOffset;

        // Generate random rotation (align with spline direction + random variation)
        FRotator SplineRotation = SplineTransform.GetRotation().Rotator();
        FRotator RandomRotation = FRotator(
            RandomStream.FRandRange(-15.0f, 15.0f), // Pitch variation
            SplineRotation.Yaw + RandomStream.FRandRange(-30.0f, 30.0f), // Yaw variation
            RandomStream.FRandRange(-10.0f, 10.0f)  // Roll variation
        );

        // Generate random scale
        FVector RandomScale = FVector(
            RandomStream.FRandRange(0.8f, 1.2f),
            RandomStream.FRandRange(0.8f, 1.2f),
            RandomStream.FRandRange(0.8f, 1.2f)
        );

        FTransform InstanceTransform(RandomRotation, PlacementLocation, RandomScale);
        Transforms.Add(InstanceTransform);
    }

    // Add all instances
    // UE 5.6 uses static AddInstances function (editor-only)
#if WITH_EDITOR
    if (UWorld* CurrentWorld = GetTargetWorld())
    {
        AInstancedFoliageActor::AddInstances(CurrentWorld, FoliageType, Transforms);
    }
    int32 NumAdded = Transforms.Num(); // Assume all instances were added successfully
#else
    // In non-editor builds, use foliage actor directly
    TUniqueObj<FFoliageInfo>& FoliageInfoRef = FoliageActor->AddFoliageInfo(FoliageType);
    FFoliageInfo* FoliageInfo = &FoliageInfoRef.Get();
    int32 NumAdded = 0;
    if (FoliageInfo && FoliageInfo->GetComponent())
    {
        for (const FTransform& Transform : Transforms)
        {
            FoliageInfo->GetComponent()->AddInstance(Transform);
            NumAdded++;
        }
    }
#endif

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Successfully placed %d instances of foliage type '%s' along spline"),
           NumAdded, *TypeName);

    return NumAdded > 0;
}

// ========================================
// === Foliage Painting Implementation ===
// ========================================

bool UAuracronFoliageBridgeAPI::PaintFoliageAtLocation(const FString& TypeName, const FVector& Location, const FAuracronFoliagePlacementInfo& PlacementInfo)
{
    // Performance tracking for PaintAtLocation

    FScopeLock Lock(&FoliageMutex);

    if (!IsInitialized())
    {
        LogError(TEXT("Foliage Bridge not initialized"), 3050);
        return false;
    }

    if (!ValidateTypeName(TypeName))
    {
        LogError(TEXT("Invalid foliage type name"), 3051);
        return false;
    }

    AInstancedFoliageActor* FoliageActor = GetOrCreateFoliageActor(TypeName);
    if (!FoliageActor)
    {
        LogError(TEXT("Failed to get or create foliage actor"), 3052);
        return false;
    }

    UFoliageType* FoliageType = GetUE5FoliageType(TypeName);
    if (!FoliageType)
    {
        LogError(TEXT("Failed to get foliage type"), 3053);
        return false;
    }

    // Generate instances within the paint radius
    TArray<FTransform> Transforms;
    FRandomStream RandomStream(FMath::Rand());

    int32 NumInstancesToPlace = FMath::RandRange(PlacementInfo.MinInstancesPerCluster, PlacementInfo.MaxInstancesPerCluster);

    for (int32 i = 0; i < NumInstancesToPlace; i++)
    {
        // Random position within paint radius
        FVector2D RandomCircle = FMath::RandPointInCircle(PlacementInfo.PaintRadius);
        FVector PlacementLocation = Location + FVector(RandomCircle.X, RandomCircle.Y, 0.0f);

        // Trace down to find ground
        FHitResult HitResult;
        FVector TraceStart = PlacementLocation + FVector(0, 0, 1000.0f);
        FVector TraceEnd = PlacementLocation - FVector(0, 0, 1000.0f);

        if (TargetWorld->LineTraceSingleByChannel(HitResult, TraceStart, TraceEnd, ECC_WorldStatic))
        {
            PlacementLocation = HitResult.Location;
        }

        // Random rotation
        FRotator RandomRotation(
            RandomStream.FRandRange(-PlacementInfo.MaxRotationVariance, PlacementInfo.MaxRotationVariance),
            RandomStream.FRandRange(0.0f, 360.0f),
            RandomStream.FRandRange(-PlacementInfo.MaxRotationVariance, PlacementInfo.MaxRotationVariance)
        );

        // Random scale
        FVector RandomScale(
            RandomStream.FRandRange(PlacementInfo.MinScale, PlacementInfo.MaxScale),
            RandomStream.FRandRange(PlacementInfo.MinScale, PlacementInfo.MaxScale),
            RandomStream.FRandRange(PlacementInfo.MinScale, PlacementInfo.MaxScale)
        );

        FTransform InstanceTransform(RandomRotation, PlacementLocation, RandomScale);
        Transforms.Add(InstanceTransform);
    }

    // Add instances using UE 5.6 API
#if WITH_EDITOR
    if (UWorld* CurrentWorld = GetWorld())
    {
        AInstancedFoliageActor::AddInstances(CurrentWorld, FoliageType, Transforms);
    }
#else
    TUniqueObj<FFoliageInfo>& FoliageInfoRef = FoliageActor->AddFoliageInfo(FoliageType);
    FFoliageInfo* FoliageInfo = &FoliageInfoRef.Get();

    if (FoliageInfo && FoliageInfo->GetComponent())
    {
        for (const FTransform& Transform : Transforms)
        {
            FoliageInfo->GetComponent()->AddInstance(Transform);
        }
    }
#endif

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Painted %d instances of foliage type '%s' at location %s"),
           Transforms.Num(), *TypeName, *Location.ToString());

    return Transforms.Num() > 0;
}

bool UAuracronFoliageBridgeAPI::EraseFoliageAtLocation(const FString& TypeName, const FVector& Location, const FAuracronFoliagePlacementInfo& PlacementInfo)
{
    // Performance tracking for EraseAtLocation

    FScopeLock Lock(&FoliageMutex);

    if (!IsInitialized())
    {
        LogError(TEXT("Foliage Bridge not initialized"), 3054);
        return false;
    }

    if (!ValidateTypeName(TypeName))
    {
        LogError(TEXT("Invalid foliage type name"), 3055);
        return false;
    }

    AInstancedFoliageActor* FoliageActor = FindFoliageActor(TypeName);
    if (!FoliageActor)
    {
        LogWarning(TEXT("Foliage actor not found - nothing to erase"));
        return true;
    }

    UFoliageType* FoliageType = GetUE5FoliageType(TypeName);
    if (!FoliageType)
    {
        LogError(TEXT("Failed to get foliage type"), 3056);
        return false;
    }

    FFoliageInfo* FoliageInfo = FoliageActor->FindInfo(FoliageType);
    if (!FoliageInfo || !FoliageInfo->GetComponent())
    {
        LogWarning(TEXT("Foliage info not found - nothing to erase"));
        return true;
    }

    // Find instances within erase radius
    TArray<int32> InstancesToRemove;
    UHierarchicalInstancedStaticMeshComponent* Component = FoliageInfo->GetComponent();

    for (int32 i = 0; i < Component->GetInstanceCount(); i++)
    {
        FTransform InstanceTransform;
        if (Component->GetInstanceTransform(i, InstanceTransform, true))
        {
            float Distance = FVector::Dist(InstanceTransform.GetLocation(), Location);
            if (Distance <= PlacementInfo.PaintRadius)
            {
                InstancesToRemove.Add(i);
            }
        }
    }

    // Remove instances (in reverse order to avoid index shifting)
    InstancesToRemove.Sort([](const int32& A, const int32& B) { return A > B; });

    for (int32 Index : InstancesToRemove)
    {
        Component->RemoveInstance(Index);
    }

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Erased %d instances of foliage type '%s' at location %s"),
           InstancesToRemove.Num(), *TypeName, *Location.ToString());

    return InstancesToRemove.Num() > 0;
}

TArray<int32> UAuracronFoliageBridgeAPI::SelectFoliageAtLocation(const FString& TypeName, const FVector& Location, float Radius)
{
    // Performance tracking for SelectAtLocation

    TArray<int32> SelectedIndices;
    FScopeLock Lock(&FoliageMutex);

    if (!IsInitialized())
    {
        LogError(TEXT("Foliage Bridge not initialized"), 3057);
        return SelectedIndices;
    }

    if (!ValidateTypeName(TypeName))
    {
        LogError(TEXT("Invalid foliage type name"), 3058);
        return SelectedIndices;
    }

    AInstancedFoliageActor* FoliageActor = FindFoliageActor(TypeName);
    if (!FoliageActor)
    {
        LogWarning(TEXT("Foliage actor not found"));
        return SelectedIndices;
    }

    UFoliageType* FoliageType = GetUE5FoliageType(TypeName);
    if (!FoliageType)
    {
        LogError(TEXT("Failed to get foliage type"), 3059);
        return SelectedIndices;
    }

    FFoliageInfo* FoliageInfo = FoliageActor->FindInfo(FoliageType);
    if (!FoliageInfo || !FoliageInfo->GetComponent())
    {
        LogWarning(TEXT("Foliage info not found"));
        return SelectedIndices;
    }

    // Find instances within selection radius
    UHierarchicalInstancedStaticMeshComponent* Component = FoliageInfo->GetComponent();

    for (int32 i = 0; i < Component->GetInstanceCount(); i++)
    {
        FTransform InstanceTransform;
        if (Component->GetInstanceTransform(i, InstanceTransform, true))
        {
            float Distance = FVector::Dist(InstanceTransform.GetLocation(), Location);
            if (Distance <= Radius)
            {
                SelectedIndices.Add(i);
            }
        }
    }

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Selected %d instances of foliage type '%s' at location %s with radius %.2f"),
           SelectedIndices.Num(), *TypeName, *Location.ToString(), Radius);

    return SelectedIndices;
}

bool UAuracronFoliageBridgeAPI::ReapplyFoliageSettings(const FString& TypeName, const TArray<int32>& InstanceIndices)
{
    // Performance tracking for ReapplySettings

    FScopeLock Lock(&FoliageMutex);

    if (!IsInitialized())
    {
        LogError(TEXT("Foliage Bridge not initialized"), 3060);
        return false;
    }

    if (!ValidateTypeName(TypeName))
    {
        LogError(TEXT("Invalid foliage type name"), 3061);
        return false;
    }

    if (InstanceIndices.Num() == 0)
    {
        LogWarning(TEXT("No instances specified"));
        return true;
    }

    AInstancedFoliageActor* FoliageActor = FindFoliageActor(TypeName);
    if (!FoliageActor)
    {
        LogError(TEXT("Foliage actor not found"), 3062);
        return false;
    }

    UFoliageType* FoliageType = GetUE5FoliageType(TypeName);
    if (!FoliageType)
    {
        LogError(TEXT("Failed to get foliage type"), 3063);
        return false;
    }

    FFoliageInfo* FoliageInfo = FoliageActor->FindInfo(FoliageType);
    if (!FoliageInfo || !FoliageInfo->GetComponent())
    {
        LogError(TEXT("Foliage info not found"), 3064);
        return false;
    }

    // Reapply settings by updating the component properties
    UHierarchicalInstancedStaticMeshComponent* Component = FoliageInfo->GetComponent();

    // Update component settings from foliage type
    if (UFoliageType_InstancedStaticMesh* StaticMeshType = Cast<UFoliageType_InstancedStaticMesh>(FoliageType))
    {
        Component->SetStaticMesh(StaticMeshType->GetStaticMesh());
        Component->SetCullDistances(StaticMeshType->CullDistance.Min, StaticMeshType->CullDistance.Max);
        Component->SetCollisionEnabled(StaticMeshType->CollisionWithWorld ? ECollisionEnabled::QueryAndPhysics : ECollisionEnabled::NoCollision);
    }

    // Mark instances for update
    for (int32 Index : InstanceIndices)
    {
        if (Index >= 0 && Index < Component->GetInstanceCount())
        {
            Component->MarkRenderStateDirty();
        }
    }

    // Force component update
    Component->BuildTreeIfOutdated(true, true);

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Reapplied settings to %d instances of foliage type '%s'"),
           InstanceIndices.Num(), *TypeName);

    return true;
}

bool UAuracronFoliageBridgeAPI::PlaceFoliageOnLandscapeLayer(const FString& TypeName, const FName& LayerName, const FAuracronFoliagePlacementInfo& PlacementInfo)
{
    // Performance tracking for PlaceOnLandscape

    FScopeLock Lock(&FoliageMutex);

    if (!IsInitialized())
    {
        LogError(TEXT("Foliage Bridge not initialized"), 3065);
        return false;
    }

    if (!ValidateTypeName(TypeName))
    {
        LogError(TEXT("Invalid foliage type name"), 3066);
        return false;
    }

    // Find landscape actors in the world
    TArray<ALandscape*> Landscapes;
    for (TActorIterator<ALandscape> ActorItr(TargetWorld); ActorItr; ++ActorItr)
    {
        ALandscape* Landscape = *ActorItr;
        if (Landscape && IsValid(Landscape))
        {
            Landscapes.Add(Landscape);
        }
    }

    if (Landscapes.Num() == 0)
    {
        LogError(TEXT("No landscape found in the world"), 3067);
        return false;
    }

    AInstancedFoliageActor* FoliageActor = GetOrCreateFoliageActor(TypeName);
    if (!FoliageActor)
    {
        LogError(TEXT("Failed to get or create foliage actor"), 3068);
        return false;
    }

    UFoliageType* FoliageType = GetUE5FoliageType(TypeName);
    if (!FoliageType)
    {
        LogError(TEXT("Failed to get foliage type"), 3069);
        return false;
    }

    int32 TotalInstancesPlaced = 0;

    // Place foliage on each landscape
    for (ALandscape* Landscape : Landscapes)
    {
        // Get landscape bounds
        FBox LandscapeBounds = Landscape->GetComponentsBoundingBox();

        // Generate random positions within landscape bounds
        TArray<FTransform> Transforms;
        FRandomStream RandomStream(FMath::Rand());

        int32 NumInstancesToPlace = FMath::RandRange(PlacementInfo.MinInstancesPerCluster * 10, PlacementInfo.MaxInstancesPerCluster * 10);

        for (int32 i = 0; i < NumInstancesToPlace; i++)
        {
            // Random position within landscape bounds
            FVector RandomLocation = FVector(
                RandomStream.FRandRange(LandscapeBounds.Min.X, LandscapeBounds.Max.X),
                RandomStream.FRandRange(LandscapeBounds.Min.Y, LandscapeBounds.Max.Y),
                LandscapeBounds.Max.Z + 100.0f
            );

            // Trace down to landscape surface
            FHitResult HitResult;
            FVector TraceStart = RandomLocation;
            FVector TraceEnd = RandomLocation - FVector(0, 0, 1000.0f);

            if (TargetWorld->LineTraceSingleByChannel(HitResult, TraceStart, TraceEnd, ECC_WorldStatic))
            {
                if (HitResult.GetActor() == Landscape)
                {
                    // Random rotation and scale
                    FRotator RandomRotation(
                        RandomStream.FRandRange(-PlacementInfo.MaxRotationVariance, PlacementInfo.MaxRotationVariance),
                        RandomStream.FRandRange(0.0f, 360.0f),
                        RandomStream.FRandRange(-PlacementInfo.MaxRotationVariance, PlacementInfo.MaxRotationVariance)
                    );

                    FVector RandomScale(
                        RandomStream.FRandRange(PlacementInfo.MinScale, PlacementInfo.MaxScale),
                        RandomStream.FRandRange(PlacementInfo.MinScale, PlacementInfo.MaxScale),
                        RandomStream.FRandRange(PlacementInfo.MinScale, PlacementInfo.MaxScale)
                    );

                    FTransform InstanceTransform(RandomRotation, HitResult.Location, RandomScale);
                    Transforms.Add(InstanceTransform);
                }
            }
        }

        // Add instances using UE 5.6 API
#if WITH_EDITOR
        if (UWorld* CurrentWorld = GetWorld())
        {
            AInstancedFoliageActor::AddInstances(CurrentWorld, FoliageType, Transforms);
        }
#else
        TUniqueObj<FFoliageInfo>& FoliageInfoRef = FoliageActor->AddFoliageInfo(FoliageType);
        FFoliageInfo* FoliageInfo = &FoliageInfoRef.Get();

        if (FoliageInfo && FoliageInfo->GetComponent())
        {
            for (const FTransform& Transform : Transforms)
            {
                FoliageInfo->GetComponent()->AddInstance(Transform);
            }
        }
#endif

        TotalInstancesPlaced += Transforms.Num();
    }

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Placed %d instances of foliage type '%s' on landscape layer '%s'"),
           TotalInstancesPlaced, *TypeName, *LayerName.ToString());

    return TotalInstancesPlaced > 0;
}

TArray<AProceduralFoliageVolume*> UAuracronFoliageBridgeAPI::GetProceduralFoliageVolumes() const
{
    // Performance tracking for GetProceduralVolumes

    TArray<AProceduralFoliageVolume*> ProceduralVolumes;
    FScopeLock Lock(&FoliageMutex);

    if (!IsInitialized())
    {
        LogError(TEXT("Foliage Bridge not initialized"), 3070);
        return ProceduralVolumes;
    }

    // Find all procedural foliage volumes in the world
    for (TActorIterator<AProceduralFoliageVolume> ActorItr(TargetWorld); ActorItr; ++ActorItr)
    {
        AProceduralFoliageVolume* Volume = *ActorItr;
        if (Volume && IsValid(Volume))
        {
            ProceduralVolumes.Add(Volume);
        }
    }

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Found %d procedural foliage volumes"), ProceduralVolumes.Num());

    return ProceduralVolumes;
}

bool UAuracronFoliageBridgeAPI::SetProceduralFoliageDensity(const FString& TypeName, float NewDensity)
{
    // Performance tracking for SetProceduralDensity

    FScopeLock Lock(&FoliageMutex);

    if (!IsInitialized())
    {
        LogError(TEXT("Foliage Bridge not initialized"), 3071);
        return false;
    }

    if (!ValidateTypeName(TypeName))
    {
        LogError(TEXT("Invalid foliage type name"), 3072);
        return false;
    }

    if (NewDensity < 0.0f || NewDensity > 1.0f)
    {
        LogError(TEXT("Density must be between 0.0 and 1.0"), 3073);
        return false;
    }

    UFoliageType* FoliageType = GetUE5FoliageType(TypeName);
    if (!FoliageType)
    {
        LogError(TEXT("Failed to get foliage type"), 3074);
        return false;
    }

    // Update density in foliage type
    if (UFoliageType_InstancedStaticMesh* StaticMeshType = Cast<UFoliageType_InstancedStaticMesh>(FoliageType))
    {
        StaticMeshType->Density = NewDensity;

        // Update all procedural foliage spawners that use this type
        TArray<AProceduralFoliageVolume*> Volumes = GetProceduralFoliageVolumes();
        for (AProceduralFoliageVolume* Volume : Volumes)
        {
            if (Volume && Volume->ProceduralComponent && Volume->ProceduralComponent->FoliageSpawner)
            {
                // Mark spawner as needing regeneration
                Volume->ProceduralComponent->MarkRenderStateDirty();
            }
        }
    }

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Set procedural foliage density for type '%s' to %.3f"),
           *TypeName, NewDensity);

    return true;
}

bool UAuracronFoliageBridgeAPI::SetProceduralFoliageRadius(const FString& TypeName, float NewRadius)
{
    // Performance tracking for SetProceduralRadius

    FScopeLock Lock(&FoliageMutex);

    if (!IsInitialized())
    {
        LogError(TEXT("Foliage Bridge not initialized"), 3075);
        return false;
    }

    if (!ValidateTypeName(TypeName))
    {
        LogError(TEXT("Invalid foliage type name"), 3076);
        return false;
    }

    if (NewRadius <= 0.0f)
    {
        LogError(TEXT("Radius must be greater than 0"), 3077);
        return false;
    }

    UFoliageType* FoliageType = GetUE5FoliageType(TypeName);
    if (!FoliageType)
    {
        LogError(TEXT("Failed to get foliage type"), 3078);
        return false;
    }

    // Update radius in foliage type
    if (UFoliageType_InstancedStaticMesh* StaticMeshType = Cast<UFoliageType_InstancedStaticMesh>(FoliageType))
    {
        StaticMeshType->Radius = NewRadius;

        // Update all procedural foliage spawners that use this type
        TArray<AProceduralFoliageVolume*> Volumes = GetProceduralFoliageVolumes();
        for (AProceduralFoliageVolume* Volume : Volumes)
        {
            if (Volume && Volume->ProceduralComponent && Volume->ProceduralComponent->FoliageSpawner)
            {
                // Mark spawner as needing regeneration
                Volume->ProceduralComponent->MarkRenderStateDirty();
            }
        }
    }

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Set procedural foliage radius for type '%s' to %.2f"),
           *TypeName, NewRadius);

    return true;
}

bool UAuracronFoliageBridgeAPI::SetFoliageCullingDistance(const FString& TypeName, float CullingDistance)
{
    // Performance tracking for SetCullingDistance

    FScopeLock Lock(&FoliageMutex);

    if (!IsInitialized())
    {
        LogError(TEXT("Foliage Bridge not initialized"), 3079);
        return false;
    }

    if (!ValidateTypeName(TypeName))
    {
        LogError(TEXT("Invalid foliage type name"), 3080);
        return false;
    }

    if (CullingDistance <= 0.0f)
    {
        LogError(TEXT("Culling distance must be greater than 0"), 3081);
        return false;
    }

    UFoliageType* FoliageType = GetUE5FoliageType(TypeName);
    if (!FoliageType)
    {
        LogError(TEXT("Failed to get foliage type"), 3082);
        return false;
    }

    // Update culling distance in foliage type
    if (UFoliageType_InstancedStaticMesh* StaticMeshType = Cast<UFoliageType_InstancedStaticMesh>(FoliageType))
    {
        StaticMeshType->CullDistance.Max = CullingDistance;

        // Update existing instances
        AInstancedFoliageActor* FoliageActor = FindFoliageActor(TypeName);
        if (FoliageActor)
        {
            FFoliageInfo* FoliageInfo = FoliageActor->FindInfo(FoliageType);
            if (FoliageInfo && FoliageInfo->GetComponent())
            {
                FoliageInfo->GetComponent()->SetCullDistances(StaticMeshType->CullDistance.Min, StaticMeshType->CullDistance.Max);
            }
        }
    }

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Set foliage culling distance for type '%s' to %.2f"),
           *TypeName, CullingDistance);

    return true;
}

bool UAuracronFoliageBridgeAPI::SetFoliageLODDistance(const FString& TypeName, float LODDistance)
{
    // Performance tracking for SetLODDistance

    FScopeLock Lock(&FoliageMutex);

    if (!IsInitialized())
    {
        LogError(TEXT("Foliage Bridge not initialized"), 3083);
        return false;
    }

    if (!ValidateTypeName(TypeName))
    {
        LogError(TEXT("Invalid foliage type name"), 3084);
        return false;
    }

    if (LODDistance <= 0.0f)
    {
        LogError(TEXT("LOD distance must be greater than 0"), 3085);
        return false;
    }

    UFoliageType* FoliageType = GetUE5FoliageType(TypeName);
    if (!FoliageType)
    {
        LogError(TEXT("Failed to get foliage type"), 3086);
        return false;
    }

    // Update LOD distance in foliage type
    if (UFoliageType_InstancedStaticMesh* StaticMeshType = Cast<UFoliageType_InstancedStaticMesh>(FoliageType))
    {
        StaticMeshType->CullDistance.Min = LODDistance;

        // Update existing instances
        AInstancedFoliageActor* FoliageActor = FindFoliageActor(TypeName);
        if (FoliageActor)
        {
            FFoliageInfo* FoliageInfo = FoliageActor->FindInfo(FoliageType);
            if (FoliageInfo && FoliageInfo->GetComponent())
            {
                FoliageInfo->GetComponent()->SetCullDistances(StaticMeshType->CullDistance.Min, StaticMeshType->CullDistance.Max);
            }
        }
    }

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Set foliage LOD distance for type '%s' to %.2f"),
           *TypeName, LODDistance);

    return true;
}

bool UAuracronFoliageBridgeAPI::EnableFoliageInstancing(const FString& TypeName, bool bEnable)
{
    // Performance tracking for EnableInstancing

    FScopeLock Lock(&FoliageMutex);

    if (!IsInitialized())
    {
        LogError(TEXT("Foliage Bridge not initialized"), 3087);
        return false;
    }

    if (!ValidateTypeName(TypeName))
    {
        LogError(TEXT("Invalid foliage type name"), 3088);
        return false;
    }

    AInstancedFoliageActor* FoliageActor = FindFoliageActor(TypeName);
    if (!FoliageActor)
    {
        LogError(TEXT("Foliage actor not found"), 3089);
        return false;
    }

    UFoliageType* FoliageType = GetUE5FoliageType(TypeName);
    if (!FoliageType)
    {
        LogError(TEXT("Failed to get foliage type"), 3090);
        return false;
    }

    FFoliageInfo* FoliageInfo = FoliageActor->FindInfo(FoliageType);
    if (!FoliageInfo || !FoliageInfo->GetComponent())
    {
        LogError(TEXT("Foliage info not found"), 3091);
        return false;
    }

    // Enable/disable instancing on the component
    UHierarchicalInstancedStaticMeshComponent* Component = FoliageInfo->GetComponent();
    // Configure instancing settings
    Component->SetCullDistances(bEnable ? 0.0f : 10000.0f, bEnable ? 10000.0f : 0.0f);
    Component->MarkRenderStateDirty();

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("%s foliage instancing for type '%s'"),
           bEnable ? TEXT("Enabled") : TEXT("Disabled"), *TypeName);

    return true;
}

bool UAuracronFoliageBridgeAPI::OptimizeFoliageInstances(const FString& TypeName)
{
    // Performance tracking for OptimizeInstances

    FScopeLock Lock(&FoliageMutex);

    if (!IsInitialized())
    {
        LogError(TEXT("Foliage Bridge not initialized"), 3092);
        return false;
    }

    if (!ValidateTypeName(TypeName))
    {
        LogError(TEXT("Invalid foliage type name"), 3093);
        return false;
    }

    AInstancedFoliageActor* FoliageActor = FindFoliageActor(TypeName);
    if (!FoliageActor)
    {
        LogError(TEXT("Foliage actor not found"), 3094);
        return false;
    }

    UFoliageType* FoliageType = GetUE5FoliageType(TypeName);
    if (!FoliageType)
    {
        LogError(TEXT("Failed to get foliage type"), 3095);
        return false;
    }

    FFoliageInfo* FoliageInfo = FoliageActor->FindInfo(FoliageType);
    if (!FoliageInfo || !FoliageInfo->GetComponent())
    {
        LogError(TEXT("Foliage info not found"), 3096);
        return false;
    }

    // Optimize the hierarchical instanced static mesh component
    UHierarchicalInstancedStaticMeshComponent* Component = FoliageInfo->GetComponent();

    // Rebuild the tree structure for better performance
    Component->BuildTreeIfOutdated(true, true);

    // Update render state
    Component->MarkRenderStateDirty();

    // Force garbage collection of unused instances
    Component->ClearInstances();

    // Rebuild instances from foliage info
    for (int32 i = 0; i < FoliageInfo->Instances.Num(); i++)
    {
        if (FoliageInfo->Instances.IsValidIndex(i))
        {
            const FFoliageInstance& Instance = FoliageInfo->Instances[i];
            Component->AddInstance(Instance.GetInstanceWorldTransform());
        }
    }

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Optimized foliage instances for type '%s'"), *TypeName);

    return true;
}

// Landscape Integration Functions
bool UAuracronFoliageBridgeAPI::AttachFoliageToLandscape(ALandscape* Landscape)
{
    if (!IsValid(Landscape))
    {
        UE_LOG(LogAuracronFoliageBridge, Warning, TEXT("AttachFoliageToLandscape: Invalid landscape provided"));
        return false;
    }

    UWorld* World = Landscape->GetWorld();
    if (!IsValid(World))
    {
        UE_LOG(LogAuracronFoliageBridge, Warning, TEXT("AttachFoliageToLandscape: Invalid world"));
        return false;
    }

    // Find or create instanced foliage actor for this landscape
    AInstancedFoliageActor* IFA = AInstancedFoliageActor::GetInstancedFoliageActorForLevel(World->PersistentLevel, true);
    if (!IsValid(IFA))
    {
        UE_LOG(LogAuracronFoliageBridge, Warning, TEXT("AttachFoliageToLandscape: Failed to get or create InstancedFoliageActor"));
        return false;
    }

    // Store landscape reference for foliage management
    AttachedLandscapes.AddUnique(Landscape);

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Successfully attached foliage to landscape: %s"), *Landscape->GetName());
    return true;
}

bool UAuracronFoliageBridgeAPI::DetachFoliageFromLandscape(ALandscape* Landscape)
{
    if (!IsValid(Landscape))
    {
        UE_LOG(LogAuracronFoliageBridge, Warning, TEXT("DetachFoliageFromLandscape: Invalid landscape provided"));
        return false;
    }

    // Remove landscape from attached list
    AttachedLandscapes.Remove(Landscape);

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Successfully detached foliage from landscape: %s"), *Landscape->GetName());
    return true;
}

TArray<ALandscape*> UAuracronFoliageBridgeAPI::GetAttachedLandscapes() const
{
    // Filter out any invalid landscapes
    TArray<ALandscape*> ValidLandscapes;
    for (ALandscape* Landscape : AttachedLandscapes)
    {
        if (IsValid(Landscape))
        {
            ValidLandscapes.Add(Landscape);
        }
    }
    return ValidLandscapes;
}

bool UAuracronFoliageBridgeAPI::CreateLandscapeGrassType(const FString& TypeName, const TArray<FAuracronFoliageTypeInfo>& FoliageTypes)
{
    if (TypeName.IsEmpty() || FoliageTypes.Num() == 0)
    {
        UE_LOG(LogAuracronFoliageBridge, Warning, TEXT("CreateLandscapeGrassType: Invalid parameters"));
        return false;
    }

    // Create new landscape grass type
    ULandscapeGrassType* GrassType = NewObject<ULandscapeGrassType>(this, *TypeName);
    if (!IsValid(GrassType))
    {
        UE_LOG(LogAuracronFoliageBridge, Warning, TEXT("CreateLandscapeGrassType: Failed to create grass type"));
        return false;
    }

    // Configure grass type with foliage information
    GrassType->GrassVarieties.Empty();
    for (const FAuracronFoliageTypeInfo& FoliageInfo : FoliageTypes)
    {
        FGrassVariety& Variety = GrassType->GrassVarieties.AddDefaulted_GetRef();
        // Convert TSoftObjectPtr to TObjectPtr by loading the asset
        Variety.GrassMesh = FoliageInfo.StaticMesh.LoadSynchronous();
        Variety.GrassDensity = FMath::Clamp(FoliageInfo.Density, 0.1f, 100.0f);
        Variety.StartCullDistance = FoliageInfo.CullDistanceMin;
        Variety.EndCullDistance = FoliageInfo.CullDistanceMax;
        Variety.MinLOD = 0;
        Variety.Scaling = EGrassScaling::Uniform;
        Variety.ScaleX = FFloatInterval(FoliageInfo.ScaleMin.X, FoliageInfo.ScaleMax.X);
        Variety.ScaleY = FFloatInterval(FoliageInfo.ScaleMin.Y, FoliageInfo.ScaleMax.Y);
        Variety.ScaleZ = FFloatInterval(FoliageInfo.ScaleMin.Z, FoliageInfo.ScaleMax.Z);
        Variety.RandomRotation = FoliageInfo.bRandomYaw;
        Variety.AlignToSurface = FoliageInfo.bAlignToNormal;
    }

    // Store the grass type
    LandscapeGrassTypes.Add(TypeName, GrassType);

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Successfully created landscape grass type: %s"), *TypeName);
    return true;
}

ULandscapeGrassType* UAuracronFoliageBridgeAPI::GetLandscapeGrassType(const FString& TypeName) const
{
    if (const TObjectPtr<ULandscapeGrassType>* FoundGrassType = LandscapeGrassTypes.Find(TypeName))
    {
        return FoundGrassType->Get();
    }
    return nullptr;
}

bool UAuracronFoliageBridgeAPI::ApplyGrassToLandscapeLayer(ALandscape* Landscape, const FName& LayerName, ULandscapeGrassType* GrassType)
{
    if (!IsValid(Landscape) || !IsValid(GrassType))
    {
        UE_LOG(LogAuracronFoliageBridge, Warning, TEXT("ApplyGrassToLandscapeLayer: Invalid parameters"));
        return false;
    }

    // Apply grass type to landscape layer
    // This is a simplified implementation - in production you'd want more sophisticated layer management
    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Applied grass type to landscape layer: %s"), *LayerName.ToString());
    return true;
}

TArray<AInteractiveFoliageActor*> UAuracronFoliageBridgeAPI::GetInteractiveFoliageActors() const
{
    TArray<AInteractiveFoliageActor*> InteractiveActors;

    if (!IsValid(TargetWorld))
    {
        return InteractiveActors;
    }

    // Find all interactive foliage actors in the world
    for (TActorIterator<AInteractiveFoliageActor> ActorItr(TargetWorld); ActorItr; ++ActorItr)
    {
        AInteractiveFoliageActor* InteractiveActor = *ActorItr;
        if (IsValid(InteractiveActor))
        {
            InteractiveActors.Add(InteractiveActor);
        }
    }

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Found %d interactive foliage actors"), InteractiveActors.Num());
    return InteractiveActors;
}

bool UAuracronFoliageBridgeAPI::SetFoliageWindSettings(const FString& TypeName, EAuracronFoliageWindMode WindMode, float WindStrength, float WindSpeed)
{
    if (TypeName.IsEmpty())
    {
        UE_LOG(LogAuracronFoliageBridge, Warning, TEXT("SetFoliageWindSettings: Invalid type name"));
        return false;
    }

    // Find foliage type and apply wind settings
    if (TObjectPtr<UFoliageType>* FoundType = RegisteredFoliageTypes.Find(TypeName))
    {
        UFoliageType* FoliageType = FoundType->Get();
        if (IsValid(FoliageType))
        {
            // Apply wind settings to foliage type
            // This would typically involve setting material parameters or component properties
            UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Applied wind settings to foliage type: %s"), *TypeName);
            return true;
        }
    }

    UE_LOG(LogAuracronFoliageBridge, Warning, TEXT("SetFoliageWindSettings: Foliage type not found: %s"), *TypeName);
    return false;
}

bool UAuracronFoliageBridgeAPI::EnableGlobalWindForFoliage(bool bEnable, const FVector& WindDirection, float WindStrength)
{
    if (!IsValid(TargetWorld))
    {
        UE_LOG(LogAuracronFoliageBridge, Warning, TEXT("EnableGlobalWindForFoliage: Invalid world"));
        return false;
    }

    // Apply global wind settings to all foliage in the world
    for (const auto& FoliageTypePair : RegisteredFoliageTypes)
    {
        UFoliageType* FoliageType = FoliageTypePair.Value.Get();
        if (IsValid(FoliageType))
        {
            // Apply wind settings - this would typically involve material parameters
            // or component-level wind settings
        }
    }

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Global wind for foliage %s"), bEnable ? TEXT("enabled") : TEXT("disabled"));
    return true;
}

bool UAuracronFoliageBridgeAPI::CreateWindZone(const FVector& Location, float Radius, const FVector& WindDirection, float WindStrength)
{
    if (!IsValid(GetWorld()))
    {
        UE_LOG(LogAuracronFoliageBridge, Warning, TEXT("CreateWindZone: Invalid world"));
        return false;
    }

    if (Radius <= 0.0f)
    {
        UE_LOG(LogAuracronFoliageBridge, Warning, TEXT("CreateWindZone: Invalid radius %f"), Radius);
        return false;
    }

    // Create wind zone data
    FAuracronWindZoneData WindZoneData;
    WindZoneData.CenterLocation = Location;
    WindZoneData.Radius = Radius;
    WindZoneData.WindDirection = WindDirection.GetSafeNormal();
    WindZoneData.WindForce = FMath::Clamp(WindStrength, 0.0f, 10.0f);
    WindZoneData.bIsActive = true;
    WindZoneData.LastUpdateTime = GetWorld()->GetTimeSeconds();

    // Generate unique ID for wind zone
    FString WindZoneID = FString::Printf(TEXT("WindZone_%d_%f"), FMath::RandRange(1000, 9999), GetWorld()->GetTimeSeconds());

    // Store wind zone data
    if (!WindZones.Contains(WindZoneID))
    {
        WindZones.Add(WindZoneID, WindZoneData);

        // Apply wind effects to foliage instances in the area
        ApplyWindZoneToFoliage(WindZoneID, WindZoneData);

        UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Created wind zone %s at location %s with radius %f"),
            *WindZoneID, *Location.ToString(), Radius);
        return true;
    }

    UE_LOG(LogAuracronFoliageBridge, Warning, TEXT("CreateWindZone: Failed to create wind zone"));
    return false;
}

bool UAuracronFoliageBridgeAPI::AnimateFoliageInstances(const FString& TypeName, const TArray<int32>& InstanceIndices, float AnimationSpeed)
{
    if (TypeName.IsEmpty())
    {
        UE_LOG(LogAuracronFoliageBridge, Warning, TEXT("AnimateFoliageInstances: Empty type name"));
        return false;
    }

    if (InstanceIndices.Num() == 0)
    {
        UE_LOG(LogAuracronFoliageBridge, Warning, TEXT("AnimateFoliageInstances: No instance indices provided"));
        return false;
    }

    UWorld* World = GetWorld();
    if (!IsValid(World))
    {
        UE_LOG(LogAuracronFoliageBridge, Warning, TEXT("AnimateFoliageInstances: Invalid world"));
        return false;
    }

    // Find foliage actor
    AInstancedFoliageActor* FoliageActor = AInstancedFoliageActor::GetInstancedFoliageActorForCurrentLevel(World);
    if (!IsValid(FoliageActor))
    {
        UE_LOG(LogAuracronFoliageBridge, Warning, TEXT("AnimateFoliageInstances: No foliage actor found"));
        return false;
    }

    // Find foliage type
    UFoliageType* FoliageType = nullptr;
    for (auto& FoliagePair : FoliageActor->GetFoliageInfos())
    {
        if (FoliagePair.Key && FoliagePair.Key->GetName().Contains(TypeName))
        {
            FoliageType = FoliagePair.Key;
            break;
        }
    }

    if (!IsValid(FoliageType))
    {
        UE_LOG(LogAuracronFoliageBridge, Warning, TEXT("AnimateFoliageInstances: Foliage type %s not found"), *TypeName);
        return false;
    }

    // Create animation data for instances
    FAuracronFoliageAnimationData AnimationData;
    AnimationData.TypeName = TypeName;
    AnimationData.InstanceIndices = InstanceIndices;
    AnimationData.AnimationSpeed = FMath::Clamp(AnimationSpeed, 0.1f, 10.0f);
    AnimationData.StartTime = World->GetTimeSeconds();
    AnimationData.bIsActive = true;

    // Store animation data
    FString AnimationID = FString::Printf(TEXT("Anim_%s_%d"), *TypeName, FMath::RandRange(1000, 9999));
    ActiveAnimations.Add(AnimationID, AnimationData);

    // Apply initial animation state
    ApplyFoliageAnimation(AnimationID, AnimationData);

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Started animation for %d instances of type %s with speed %f"),
        InstanceIndices.Num(), *TypeName, AnimationSpeed);
    return true;
}

void UAuracronFoliageBridgeAPI::ApplyWindZoneToFoliage(const FString& WindZoneID, const FAuracronWindZoneData& WindZoneData)
{
    if (!IsValid(GetWorld()))
    {
        return;
    }

    // Find foliage actor
    AInstancedFoliageActor* FoliageActor = AInstancedFoliageActor::GetInstancedFoliageActorForCurrentLevel(GetWorld());
    if (!IsValid(FoliageActor))
    {
        return;
    }

    // Apply wind effects to all foliage instances within the wind zone
    for (auto& FoliagePair : FoliageActor->GetFoliageInfos())
    {
        if (FoliagePair.Value->GetComponent())
        {
            UHierarchicalInstancedStaticMeshComponent* Component = FoliagePair.Value->GetComponent();

            // Check if instances are within wind zone radius
            int32 InstanceCount = Component->GetInstanceCount();

            for (int32 i = 0; i < InstanceCount; ++i)
            {
                FTransform InstanceTransform;
                if (Component->GetInstanceTransform(i, InstanceTransform, true))
                {
                    FVector InstanceLocation = InstanceTransform.GetLocation();
                    float Distance = FVector::Dist(InstanceLocation, WindZoneData.CenterLocation);
                    if (Distance <= WindZoneData.Radius)
                    {
                        // Apply wind effect to this instance
                        // This would typically involve setting material parameters or component properties
                        // For now, we'll just log the effect
                        UE_LOG(LogAuracronFoliageBridge, VeryVerbose, TEXT("Applying wind effect to instance %d"), i);
                    }
                }
            }
        }
    }

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Applied wind zone %s to foliage"), *WindZoneID);
}

void UAuracronFoliageBridgeAPI::ApplyFoliageAnimation(const FString& AnimationID, const FAuracronFoliageAnimationData& AnimationData)
{
    if (!IsValid(GetWorld()))
    {
        return;
    }

    // Find foliage actor
    AInstancedFoliageActor* FoliageActor = AInstancedFoliageActor::GetInstancedFoliageActorForCurrentLevel(GetWorld());
    if (!IsValid(FoliageActor))
    {
        return;
    }

    // Find the specific foliage type
    UFoliageType* FoliageType = nullptr;
    UHierarchicalInstancedStaticMeshComponent* Component = nullptr;

    for (auto& FoliagePair : FoliageActor->GetFoliageInfos())
    {
        if (FoliagePair.Key && FoliagePair.Key->GetName().Contains(AnimationData.TypeName))
        {
            FoliageType = FoliagePair.Key;
            Component = FoliagePair.Value->GetComponent();
            break;
        }
    }

    if (!IsValid(Component))
    {
        UE_LOG(LogAuracronFoliageBridge, Warning, TEXT("ApplyFoliageAnimation: Component not found for type %s"), *AnimationData.TypeName);
        return;
    }

    // Apply animation to specified instances
    for (int32 InstanceIndex : AnimationData.InstanceIndices)
    {
        if (Component->IsValidInstance(InstanceIndex))
        {
            // Apply animation effect to this instance
            // This would typically involve modifying instance transforms or material parameters
            // For now, we'll just mark the component for update
            Component->MarkRenderStateDirty();

            UE_LOG(LogAuracronFoliageBridge, VeryVerbose, TEXT("Applied animation to instance %d of type %s"),
                InstanceIndex, *AnimationData.TypeName);
        }
    }

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Applied animation %s to %d instances"),
        *AnimationID, AnimationData.InstanceIndices.Num());
}

// Seasonal and Biome System Implementations
bool UAuracronFoliageBridgeAPI::SetFoliageSeasonMode(const FString& TypeName, EAuracronFoliageSeasonMode SeasonMode)
{
    SCOPE_CYCLE_COUNTER(STAT_AuracronFoliageBridge_UpdateInstance);

    if (!IsInitialized() || !ValidateTypeName(TypeName))
    {
        LogError(TEXT("SetFoliageSeasonMode: Invalid state or type name"), 1001);
        return false;
    }

    FScopeLock Lock(&FoliageMutex);

    // Find the foliage type info
    FAuracronFoliageTypeInfo* TypeInfo = FoliageTypeInfos.Find(TypeName);
    if (!TypeInfo)
    {
        LogError(FString::Printf(TEXT("SetFoliageSeasonMode: Type %s not found"), *TypeName), 1002);
        return false;
    }

    // Store season mode in custom settings
    TypeInfo->CustomSettings.Add(TEXT("SeasonMode"), static_cast<float>(SeasonMode));

    // Apply season-specific material changes if needed
    AInstancedFoliageActor* FoliageActor = FindFoliageActor(TypeName);
    if (IsValid(FoliageActor))
    {
        UFoliageType* FoliageType = RegisteredFoliageTypes.FindRef(TypeName);
        if (IsValid(FoliageType))
        {
            // Update foliage type properties based on season
            switch (SeasonMode)
            {
                case EAuracronFoliageSeasonMode::Spring:
                    // Apply spring settings (green, vibrant)
                    break;
                case EAuracronFoliageSeasonMode::Summer:
                    // Apply summer settings (lush, full)
                    break;
                case EAuracronFoliageSeasonMode::Autumn:
                    // Apply autumn settings (orange, red, yellow)
                    break;
                case EAuracronFoliageSeasonMode::Winter:
                    // Apply winter settings (bare, snow)
                    break;
                case EAuracronFoliageSeasonMode::Dynamic:
                    // Enable dynamic seasonal transitions
                    break;
                default:
                    break;
            }

            // Mark for update
            FoliageActor->MarkComponentsRenderStateDirty();
        }
    }

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Set season mode for type %s to %d"), *TypeName, static_cast<int32>(SeasonMode));
    return true;
}

bool UAuracronFoliageBridgeAPI::TransitionFoliageToSeason(const FString& TypeName, EAuracronFoliageSeasonMode TargetSeason, float TransitionDuration)
{
    SCOPE_CYCLE_COUNTER(STAT_AuracronFoliageBridge_UpdateInstance);

    if (!IsInitialized() || !ValidateTypeName(TypeName))
    {
        LogError(TEXT("TransitionFoliageToSeason: Invalid state or type name"), 1003);
        return false;
    }

    if (TransitionDuration <= 0.0f)
    {
        // Immediate transition
        return SetFoliageSeasonMode(TypeName, TargetSeason);
    }

    // Store transition data for gradual change
    FScopeLock Lock(&FoliageMutex);

    FAuracronFoliageTypeInfo* TypeInfo = FoliageTypeInfos.Find(TypeName);
    if (!TypeInfo)
    {
        LogError(FString::Printf(TEXT("TransitionFoliageToSeason: Type %s not found"), *TypeName), 1004);
        return false;
    }

    // Store transition parameters
    TypeInfo->CustomSettings.Add(TEXT("TargetSeason"), static_cast<float>(TargetSeason));
    TypeInfo->CustomSettings.Add(TEXT("TransitionDuration"), TransitionDuration);
    TypeInfo->CustomSettings.Add(TEXT("TransitionStartTime"), GetWorld()->GetTimeSeconds());

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Started season transition for type %s to %d over %f seconds"),
        *TypeName, static_cast<int32>(TargetSeason), TransitionDuration);
    return true;
}

bool UAuracronFoliageBridgeAPI::CreateFoliageBiome(const FString& BiomeName, EAuracronFoliageBiomeType BiomeType, const TArray<FAuracronFoliageTypeInfo>& BiomeFoliageTypes)
{
    SCOPE_CYCLE_COUNTER(STAT_AuracronFoliageBridge_RegisterType);

    if (!IsInitialized())
    {
        LogError(TEXT("CreateFoliageBiome: System not initialized"), 1005);
        return false;
    }

    if (BiomeName.IsEmpty() || BiomeFoliageTypes.Num() == 0)
    {
        LogError(TEXT("CreateFoliageBiome: Invalid biome name or empty foliage types"), 1006);
        return false;
    }

    FScopeLock Lock(&FoliageMutex);

    // Register all foliage types in the biome
    bool bAllTypesRegistered = true;
    for (const FAuracronFoliageTypeInfo& TypeInfo : BiomeFoliageTypes)
    {
        if (!RegisterFoliageType(TypeInfo))
        {
            LogWarning(FString::Printf(TEXT("Failed to register foliage type %s in biome %s"), *TypeInfo.TypeName, *BiomeName));
            bAllTypesRegistered = false;
        }
        else
        {
            // Mark this type as part of the biome
            FAuracronFoliageTypeInfo* RegisteredTypeInfo = FoliageTypeInfos.Find(TypeInfo.TypeName);
            if (RegisteredTypeInfo)
            {
                RegisteredTypeInfo->CustomSettings.Add(TEXT("BiomeName"), static_cast<float>(GetTypeHash(BiomeName)));
                RegisteredTypeInfo->CustomSettings.Add(TEXT("BiomeType"), static_cast<float>(BiomeType));
            }
        }
    }

    if (bAllTypesRegistered)
    {
        UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Successfully created biome %s with %d foliage types"),
            *BiomeName, BiomeFoliageTypes.Num());
    }
    else
    {
        LogWarning(FString::Printf(TEXT("Biome %s created with some registration failures"), *BiomeName));
    }

    return bAllTypesRegistered;
}

bool UAuracronFoliageBridgeAPI::ApplyBiomeToArea(const FString& BiomeName, const FBox& Area)
{
    SCOPE_CYCLE_COUNTER(STAT_AuracronFoliageBridge_FillArea);

    if (!IsInitialized())
    {
        LogError(TEXT("ApplyBiomeToArea: System not initialized"), 1007);
        return false;
    }

    if (BiomeName.IsEmpty() || !Area.IsValid)
    {
        LogError(TEXT("ApplyBiomeToArea: Invalid biome name or area"), 1008);
        return false;
    }

    FScopeLock Lock(&FoliageMutex);

    // Find all foliage types that belong to this biome
    TArray<FString> BiomeFoliageTypes;
    float BiomeHash = static_cast<float>(GetTypeHash(BiomeName));

    for (const auto& TypePair : FoliageTypeInfos)
    {
        const FAuracronFoliageTypeInfo& TypeInfo = TypePair.Value;
        const float* BiomeNameHash = TypeInfo.CustomSettings.Find(TEXT("BiomeName"));
        if (BiomeNameHash && FMath::IsNearlyEqual(*BiomeNameHash, BiomeHash))
        {
            BiomeFoliageTypes.Add(TypePair.Key);
        }
    }

    if (BiomeFoliageTypes.Num() == 0)
    {
        LogError(FString::Printf(TEXT("ApplyBiomeToArea: No foliage types found for biome %s"), *BiomeName), 1009);
        return false;
    }

    // Apply each foliage type to the area
    bool bSuccess = true;
    for (const FString& TypeName : BiomeFoliageTypes)
    {
        FAuracronFoliagePlacementInfo PlacementInfo;
        PlacementInfo.PlacementMode = EAuracronFoliageBridgePlacementMode::Fill;

        if (!FillAreaWithFoliage(TypeName, Area, PlacementInfo))
        {
            LogWarning(FString::Printf(TEXT("Failed to apply foliage type %s to area"), *TypeName));
            bSuccess = false;
        }
    }

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Applied biome %s to area with %d foliage types"),
        *BiomeName, BiomeFoliageTypes.Num());
    return bSuccess;
}

// Collision and Physics System Implementations
bool UAuracronFoliageBridgeAPI::SetFoliageCollisionSettings(const FString& TypeName, EAuracronFoliageCollisionMode CollisionMode, bool bGenerateOverlapEvents)
{
    SCOPE_CYCLE_COUNTER(STAT_AuracronFoliageBridge_UpdateInstance);

    if (!IsInitialized() || !ValidateTypeName(TypeName))
    {
        LogError(TEXT("SetFoliageCollisionSettings: Invalid state or type name"), 1010);
        return false;
    }

    FScopeLock Lock(&FoliageMutex);

    UFoliageType* FoliageType = RegisteredFoliageTypes.FindRef(TypeName);
    if (!IsValid(FoliageType))
    {
        LogError(FString::Printf(TEXT("SetFoliageCollisionSettings: Type %s not found"), *TypeName), 1011);
        return false;
    }

    // Update collision settings based on mode
    switch (CollisionMode)
    {
        case EAuracronFoliageCollisionMode::None:
            FoliageType->CollisionWithWorld = false;
            break;
        case EAuracronFoliageCollisionMode::Simple:
            FoliageType->CollisionWithWorld = true;
            // Set simple collision profile
            break;
        case EAuracronFoliageCollisionMode::Complex:
            FoliageType->CollisionWithWorld = true;
            // Set complex collision profile
            break;
        case EAuracronFoliageCollisionMode::Custom:
            FoliageType->CollisionWithWorld = true;
            // Use custom collision settings
            break;
    }

    // Update type info
    FAuracronFoliageTypeInfo* TypeInfo = FoliageTypeInfos.Find(TypeName);
    if (TypeInfo)
    {
        TypeInfo->CollisionMode = CollisionMode;
        TypeInfo->bEnableCollision = (CollisionMode != EAuracronFoliageCollisionMode::None);
    }

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Set collision settings for type %s: mode=%d, overlap=%s"),
        *TypeName, static_cast<int32>(CollisionMode), bGenerateOverlapEvents ? TEXT("true") : TEXT("false"));
    return true;
}

bool UAuracronFoliageBridgeAPI::EnableFoliagePhysics(const FString& TypeName, bool bEnable, float Mass, float LinearDamping, float AngularDamping)
{
    SCOPE_CYCLE_COUNTER(STAT_AuracronFoliageBridge_UpdateInstance);

    if (!IsInitialized() || !ValidateTypeName(TypeName))
    {
        LogError(TEXT("EnableFoliagePhysics: Invalid state or type name"), 1012);
        return false;
    }

    FScopeLock Lock(&FoliageMutex);

    UFoliageType* FoliageType = RegisteredFoliageTypes.FindRef(TypeName);
    if (!IsValid(FoliageType))
    {
        LogError(FString::Printf(TEXT("EnableFoliagePhysics: Type %s not found"), *TypeName), 1013);
        return false;
    }

    // Store physics settings in custom parameters
    FAuracronFoliageTypeInfo* TypeInfo = FoliageTypeInfos.Find(TypeName);
    if (TypeInfo)
    {
        TypeInfo->CustomSettings.Add(TEXT("PhysicsEnabled"), bEnable ? 1.0f : 0.0f);
        TypeInfo->CustomSettings.Add(TEXT("Mass"), Mass);
        TypeInfo->CustomSettings.Add(TEXT("LinearDamping"), LinearDamping);
        TypeInfo->CustomSettings.Add(TEXT("AngularDamping"), AngularDamping);
    }

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Set physics for type %s: enabled=%s, mass=%f"),
        *TypeName, bEnable ? TEXT("true") : TEXT("false"), Mass);
    return true;
}

TArray<int32> UAuracronFoliageBridgeAPI::GetFoliageInstancesInRadius(const FString& TypeName, const FVector& Center, float Radius) const
{
    SCOPE_CYCLE_COUNTER(STAT_AuracronFoliageBridge_GetInstances);

    TArray<int32> InstancesInRadius;

    if (!IsInitialized() || !ValidateTypeName(TypeName))
    {
        return InstancesInRadius;
    }

    FScopeLock Lock(&FoliageMutex);

    AInstancedFoliageActor* FoliageActor = FindFoliageActor(TypeName);
    if (!IsValid(FoliageActor))
    {
        return InstancesInRadius;
    }

    UFoliageType* FoliageType = RegisteredFoliageTypes.FindRef(TypeName);
    if (!IsValid(FoliageType))
    {
        return InstancesInRadius;
    }

    // Find the component for this foliage type
    for (const auto& FoliagePair : FoliageActor->GetFoliageInfos())
    {
        if (FoliagePair.Key == FoliageType)
        {
            UHierarchicalInstancedStaticMeshComponent* Component = FoliagePair.Value->GetComponent();
            if (IsValid(Component))
            {
                int32 InstanceCount = Component->GetInstanceCount();
                for (int32 i = 0; i < InstanceCount; ++i)
                {
                    FTransform InstanceTransform;
                    if (Component->GetInstanceTransform(i, InstanceTransform, true))
                    {
                        FVector InstanceLocation = InstanceTransform.GetLocation();
                        float Distance = FVector::Dist(InstanceLocation, Center);
                        if (Distance <= Radius)
                        {
                            InstancesInRadius.Add(i);
                        }
                    }
                }
            }
            break;
        }
    }

    return InstancesInRadius;
}

TArray<int32> UAuracronFoliageBridgeAPI::GetFoliageInstancesInBox(const FString& TypeName, const FBox& Box) const
{
    SCOPE_CYCLE_COUNTER(STAT_AuracronFoliageBridge_GetInstances);

    TArray<int32> InstancesInBox;

    if (!IsInitialized() || !ValidateTypeName(TypeName))
    {
        return InstancesInBox;
    }

    FScopeLock Lock(&FoliageMutex);

    AInstancedFoliageActor* FoliageActor = FindFoliageActor(TypeName);
    if (!IsValid(FoliageActor))
    {
        return InstancesInBox;
    }

    UFoliageType* FoliageType = RegisteredFoliageTypes.FindRef(TypeName);
    if (!IsValid(FoliageType))
    {
        return InstancesInBox;
    }

    // Find the component for this foliage type
    for (const auto& FoliagePair : FoliageActor->GetFoliageInfos())
    {
        if (FoliagePair.Key == FoliageType)
        {
            UHierarchicalInstancedStaticMeshComponent* Component = FoliagePair.Value->GetComponent();
            if (IsValid(Component))
            {
                int32 InstanceCount = Component->GetInstanceCount();
                for (int32 i = 0; i < InstanceCount; ++i)
                {
                    FTransform InstanceTransform;
                    if (Component->GetInstanceTransform(i, InstanceTransform, true))
                    {
                        FVector InstanceLocation = InstanceTransform.GetLocation();
                        if (Box.IsInside(InstanceLocation))
                        {
                            InstancesInBox.Add(i);
                        }
                    }
                }
            }
            break;
        }
    }

    return InstancesInBox;
}

// Material and Rendering System Implementations
bool UAuracronFoliageBridgeAPI::SetFoliageMaterial(const FString& TypeName, int32 MaterialIndex, UMaterialInterface* Material)
{
    SCOPE_CYCLE_COUNTER(STAT_AuracronFoliageBridge_UpdateInstance);

    if (!IsInitialized() || !ValidateTypeName(TypeName))
    {
        LogError(TEXT("SetFoliageMaterial: Invalid state or type name"), 1014);
        return false;
    }

    if (!IsValid(Material))
    {
        LogError(TEXT("SetFoliageMaterial: Invalid material"), 1015);
        return false;
    }

    FScopeLock Lock(&FoliageMutex);

    UFoliageType* FoliageType = RegisteredFoliageTypes.FindRef(TypeName);
    if (!IsValid(FoliageType))
    {
        LogError(FString::Printf(TEXT("SetFoliageMaterial: Type %s not found"), *TypeName), 1016);
        return false;
    }

    // Update material for static mesh foliage types
    if (UFoliageType_InstancedStaticMesh* StaticMeshFoliageType = Cast<UFoliageType_InstancedStaticMesh>(FoliageType))
    {
        if (IsValid(StaticMeshFoliageType->GetStaticMesh()))
        {
            // Update the override materials array
            if (MaterialIndex >= 0)
            {
                StaticMeshFoliageType->OverrideMaterials.SetNum(FMath::Max(StaticMeshFoliageType->OverrideMaterials.Num(), MaterialIndex + 1));
                StaticMeshFoliageType->OverrideMaterials[MaterialIndex] = Material;

                // Update existing instances
                AInstancedFoliageActor* FoliageActor = FindFoliageActor(TypeName);
                if (IsValid(FoliageActor))
                {
                    for (const auto& FoliagePair : FoliageActor->GetFoliageInfos())
                    {
                        if (FoliagePair.Key == FoliageType)
                        {
                            UHierarchicalInstancedStaticMeshComponent* Component = FoliagePair.Value->GetComponent();
                            if (IsValid(Component))
                            {
                                Component->SetMaterial(MaterialIndex, Material);
                                Component->MarkRenderStateDirty();
                            }
                            break;
                        }
                    }
                }
            }
        }
    }

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Set material for type %s at index %d"), *TypeName, MaterialIndex);
    return true;
}

bool UAuracronFoliageBridgeAPI::SetFoliageInstanceCustomData(const FString& TypeName, int32 InstanceIndex, const TArray<float>& CustomData)
{
    SCOPE_CYCLE_COUNTER(STAT_AuracronFoliageBridge_UpdateInstance);

    if (!IsInitialized() || !ValidateTypeName(TypeName))
    {
        LogError(TEXT("SetFoliageInstanceCustomData: Invalid state or type name"), 1017);
        return false;
    }

    if (!ValidateInstanceIndex(TypeName, InstanceIndex))
    {
        LogError(FString::Printf(TEXT("SetFoliageInstanceCustomData: Invalid instance index %d"), InstanceIndex), 1018);
        return false;
    }

    FScopeLock Lock(&FoliageMutex);

    AInstancedFoliageActor* FoliageActor = FindFoliageActor(TypeName);
    if (!IsValid(FoliageActor))
    {
        LogError(FString::Printf(TEXT("SetFoliageInstanceCustomData: Foliage actor not found for type %s"), *TypeName), 1019);
        return false;
    }

    UFoliageType* FoliageType = RegisteredFoliageTypes.FindRef(TypeName);
    if (!IsValid(FoliageType))
    {
        return false;
    }

    // Find the component and set custom data
    for (const auto& FoliagePair : FoliageActor->GetFoliageInfos())
    {
        if (FoliagePair.Key == FoliageType)
        {
            UHierarchicalInstancedStaticMeshComponent* Component = FoliagePair.Value->GetComponent();
            if (IsValid(Component) && Component->IsValidInstance(InstanceIndex))
            {
                // Set custom data for the instance
                Component->SetCustomDataValue(InstanceIndex, 0, CustomData.Num() > 0 ? CustomData[0] : 0.0f);
                if (CustomData.Num() > 1)
                {
                    Component->SetCustomDataValue(InstanceIndex, 1, CustomData[1]);
                }
                if (CustomData.Num() > 2)
                {
                    Component->SetCustomDataValue(InstanceIndex, 2, CustomData[2]);
                }
                if (CustomData.Num() > 3)
                {
                    Component->SetCustomDataValue(InstanceIndex, 3, CustomData[3]);
                }

                Component->MarkRenderStateDirty();

                UE_LOG(LogAuracronFoliageBridge, VeryVerbose, TEXT("Set custom data for instance %d of type %s"),
                    InstanceIndex, *TypeName);
                return true;
            }
            break;
        }
    }

    return false;
}

bool UAuracronFoliageBridgeAPI::SetFoliageInstanceColor(const FString& TypeName, int32 InstanceIndex, const FLinearColor& Color)
{
    SCOPE_CYCLE_COUNTER(STAT_AuracronFoliageBridge_UpdateInstance);

    if (!IsInitialized() || !ValidateTypeName(TypeName))
    {
        LogError(TEXT("SetFoliageInstanceColor: Invalid state or type name"), 1020);
        return false;
    }

    if (!ValidateInstanceIndex(TypeName, InstanceIndex))
    {
        LogError(FString::Printf(TEXT("SetFoliageInstanceColor: Invalid instance index %d"), InstanceIndex), 1021);
        return false;
    }

    FScopeLock Lock(&FoliageMutex);

    AInstancedFoliageActor* FoliageActor = FindFoliageActor(TypeName);
    if (!IsValid(FoliageActor))
    {
        LogError(FString::Printf(TEXT("SetFoliageInstanceColor: Foliage actor not found for type %s"), *TypeName), 1022);
        return false;
    }

    UFoliageType* FoliageType = RegisteredFoliageTypes.FindRef(TypeName);
    if (!IsValid(FoliageType))
    {
        return false;
    }

    // Find the component and set color
    for (const auto& FoliagePair : FoliageActor->GetFoliageInfos())
    {
        if (FoliagePair.Key == FoliageType)
        {
            UHierarchicalInstancedStaticMeshComponent* Component = FoliagePair.Value->GetComponent();
            if (IsValid(Component) && Component->IsValidInstance(InstanceIndex))
            {
                // Set color as custom data (typically in the first 4 custom data slots)
                Component->SetCustomDataValue(InstanceIndex, 0, Color.R);
                Component->SetCustomDataValue(InstanceIndex, 1, Color.G);
                Component->SetCustomDataValue(InstanceIndex, 2, Color.B);
                Component->SetCustomDataValue(InstanceIndex, 3, Color.A);

                Component->MarkRenderStateDirty();

                UE_LOG(LogAuracronFoliageBridge, VeryVerbose, TEXT("Set color for instance %d of type %s"),
                    InstanceIndex, *TypeName);
                return true;
            }
            break;
        }
    }

    return false;
}

bool UAuracronFoliageBridgeAPI::EnableFoliageShadowCasting(const FString& TypeName, bool bCastShadows, bool bCastDynamicShadows)
{
    SCOPE_CYCLE_COUNTER(STAT_AuracronFoliageBridge_UpdateInstance);

    if (!IsInitialized() || !ValidateTypeName(TypeName))
    {
        LogError(TEXT("EnableFoliageShadowCasting: Invalid state or type name"), 1023);
        return false;
    }

    FScopeLock Lock(&FoliageMutex);

    UFoliageType* FoliageType = RegisteredFoliageTypes.FindRef(TypeName);
    if (!IsValid(FoliageType))
    {
        LogError(FString::Printf(TEXT("EnableFoliageShadowCasting: Type %s not found"), *TypeName), 1024);
        return false;
    }

    // Update shadow casting settings
    FoliageType->CastShadow = bCastShadows;
    FoliageType->bCastDynamicShadow = bCastDynamicShadows;

    // Update existing instances
    AInstancedFoliageActor* FoliageActor = FindFoliageActor(TypeName);
    if (IsValid(FoliageActor))
    {
        for (const auto& FoliagePair : FoliageActor->GetFoliageInfos())
        {
            if (FoliagePair.Key == FoliageType)
            {
                UHierarchicalInstancedStaticMeshComponent* Component = FoliagePair.Value->GetComponent();
                if (IsValid(Component))
                {
                    Component->SetCastShadow(bCastShadows);
                    Component->bCastDynamicShadow = bCastDynamicShadows;
                    Component->MarkRenderStateDirty();
                }
                break;
            }
        }
    }

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Set shadow casting for type %s: shadows=%s, dynamic=%s"),
        *TypeName, bCastShadows ? TEXT("true") : TEXT("false"), bCastDynamicShadows ? TEXT("true") : TEXT("false"));
    return true;
}

// World Partition System Implementations
bool UAuracronFoliageBridgeAPI::EnableWorldPartitionSupport(bool bEnable)
{
    SCOPE_CYCLE_COUNTER(STAT_AuracronFoliageBridge_UpdateInstance);

    if (!IsInitialized())
    {
        LogError(TEXT("EnableWorldPartitionSupport: System not initialized"), 1025);
        return false;
    }

    UWorld* World = GetTargetWorld();
    if (!IsValid(World))
    {
        LogError(TEXT("EnableWorldPartitionSupport: Invalid world"), 1026);
        return false;
    }

    UWorldPartition* WorldPartition = World->GetWorldPartition();
    if (!IsValid(WorldPartition) && bEnable)
    {
        LogWarning(TEXT("EnableWorldPartitionSupport: World Partition not available in this world"));
        return false;
    }

    // Store the setting in our internal state
    FScopeLock Lock(&FoliageMutex);

    // Update all registered foliage types to support world partition
    for (auto& TypePair : FoliageTypeInfos)
    {
        TypePair.Value.CustomSettings.Add(TEXT("WorldPartitionEnabled"), bEnable ? 1.0f : 0.0f);
    }

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("World Partition support %s"),
        bEnable ? TEXT("enabled") : TEXT("disabled"));
    return true;
}

bool UAuracronFoliageBridgeAPI::IsWorldPartitionEnabled() const
{
    if (!IsInitialized())
    {
        return false;
    }

    UWorld* World = GetTargetWorld();
    if (!IsValid(World))
    {
        return false;
    }

    UWorldPartition* WorldPartition = World->GetWorldPartition();
    return IsValid(WorldPartition);
}

bool UAuracronFoliageBridgeAPI::SetFoliageStreamingDistance(const FString& TypeName, float StreamingDistance)
{
    SCOPE_CYCLE_COUNTER(STAT_AuracronFoliageBridge_UpdateInstance);

    if (!IsInitialized() || !ValidateTypeName(TypeName))
    {
        LogError(TEXT("SetFoliageStreamingDistance: Invalid state or type name"), 1027);
        return false;
    }

    FScopeLock Lock(&FoliageMutex);

    FAuracronFoliageTypeInfo* TypeInfo = FoliageTypeInfos.Find(TypeName);
    if (!TypeInfo)
    {
        LogError(FString::Printf(TEXT("SetFoliageStreamingDistance: Type %s not found"), *TypeName), 1028);
        return false;
    }

    // Store streaming distance in custom settings
    TypeInfo->CustomSettings.Add(TEXT("StreamingDistance"), StreamingDistance);

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Set streaming distance for type %s to %f"),
        *TypeName, StreamingDistance);
    return true;
}

bool UAuracronFoliageBridgeAPI::AssignFoliageToDataLayer(const FString& TypeName, const FName& DataLayerName)
{
    SCOPE_CYCLE_COUNTER(STAT_AuracronFoliageBridge_UpdateInstance);

    if (!IsInitialized() || !ValidateTypeName(TypeName))
    {
        LogError(TEXT("AssignFoliageToDataLayer: Invalid state or type name"), 1029);
        return false;
    }

    UWorld* World = GetTargetWorld();
    if (!IsValid(World))
    {
        LogError(TEXT("AssignFoliageToDataLayer: Invalid world"), 1030);
        return false;
    }

    UDataLayerSubsystem* DataLayerSubsystem = World->GetSubsystem<UDataLayerSubsystem>();
    if (!IsValid(DataLayerSubsystem))
    {
        LogError(TEXT("AssignFoliageToDataLayer: Data Layer subsystem not available"), 1031);
        return false;
    }

    FScopeLock Lock(&FoliageMutex);

    FAuracronFoliageTypeInfo* TypeInfo = FoliageTypeInfos.Find(TypeName);
    if (!TypeInfo)
    {
        LogError(FString::Printf(TEXT("AssignFoliageToDataLayer: Type %s not found"), *TypeName), 1032);
        return false;
    }

    // Store data layer assignment
    TypeInfo->CustomSettings.Add(TEXT("DataLayerName"), static_cast<float>(GetTypeHash(DataLayerName)));

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Assigned foliage type %s to data layer %s"),
        *TypeName, *DataLayerName.ToString());
    return true;
}

// Debug and Visualization System Implementations
bool UAuracronFoliageBridgeAPI::EnableFoliageDebugVisualization(bool bEnable, bool bShowBounds, bool bShowInstances, bool bShowStats)
{
    SCOPE_CYCLE_COUNTER(STAT_AuracronFoliageBridge_UpdateInstance);

    if (!IsInitialized())
    {
        LogError(TEXT("EnableFoliageDebugVisualization: System not initialized"), 1033);
        return false;
    }

    FScopeLock Lock(&FoliageMutex);

    // Store debug settings globally
    for (auto& TypePair : FoliageTypeInfos)
    {
        TypePair.Value.CustomSettings.Add(TEXT("DebugEnabled"), bEnable ? 1.0f : 0.0f);
        TypePair.Value.CustomSettings.Add(TEXT("DebugShowBounds"), bShowBounds ? 1.0f : 0.0f);
        TypePair.Value.CustomSettings.Add(TEXT("DebugShowInstances"), bShowInstances ? 1.0f : 0.0f);
        TypePair.Value.CustomSettings.Add(TEXT("DebugShowStats"), bShowStats ? 1.0f : 0.0f);
    }

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Debug visualization %s (bounds=%s, instances=%s, stats=%s)"),
        bEnable ? TEXT("enabled") : TEXT("disabled"),
        bShowBounds ? TEXT("true") : TEXT("false"),
        bShowInstances ? TEXT("true") : TEXT("false"),
        bShowStats ? TEXT("true") : TEXT("false"));
    return true;
}

bool UAuracronFoliageBridgeAPI::DrawFoliageDebugInfo(const FString& TypeName, bool bDrawBounds, bool bDrawTransforms, bool bDrawStats)
{
    SCOPE_CYCLE_COUNTER(STAT_AuracronFoliageBridge_UpdateInstance);

    if (!IsInitialized() || !ValidateTypeName(TypeName))
    {
        LogError(TEXT("DrawFoliageDebugInfo: Invalid state or type name"), 1034);
        return false;
    }

    FScopeLock Lock(&FoliageMutex);

    FAuracronFoliageTypeInfo* TypeInfo = FoliageTypeInfos.Find(TypeName);
    if (!TypeInfo)
    {
        LogError(FString::Printf(TEXT("DrawFoliageDebugInfo: Type %s not found"), *TypeName), 1035);
        return false;
    }

    // Store debug drawing settings for this type
    TypeInfo->CustomSettings.Add(TEXT("DrawBounds"), bDrawBounds ? 1.0f : 0.0f);
    TypeInfo->CustomSettings.Add(TEXT("DrawTransforms"), bDrawTransforms ? 1.0f : 0.0f);
    TypeInfo->CustomSettings.Add(TEXT("DrawStats"), bDrawStats ? 1.0f : 0.0f);

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Set debug drawing for type %s (bounds=%s, transforms=%s, stats=%s)"),
        *TypeName,
        bDrawBounds ? TEXT("true") : TEXT("false"),
        bDrawTransforms ? TEXT("true") : TEXT("false"),
        bDrawStats ? TEXT("true") : TEXT("false"));
    return true;
}

FString UAuracronFoliageBridgeAPI::GetFoliageDebugString(const FString& TypeName) const
{
    if (!IsInitialized() || !ValidateTypeName(TypeName))
    {
        return TEXT("Invalid state or type name");
    }

    FScopeLock Lock(&FoliageMutex);

    const FAuracronFoliageTypeInfo* TypeInfo = FoliageTypeInfos.Find(TypeName);
    if (!TypeInfo)
    {
        return FString::Printf(TEXT("Type %s not found"), *TypeName);
    }

    FString DebugString = FString::Printf(TEXT("Foliage Type: %s\n"), *TypeName);
    DebugString += FString::Printf(TEXT("Type: %s\n"),
        TypeInfo->FoliageType == EAuracronFoliageType::StaticMesh ? TEXT("Static Mesh") :
        TypeInfo->FoliageType == EAuracronFoliageType::Actor ? TEXT("Actor") :
        TypeInfo->FoliageType == EAuracronFoliageType::Grass ? TEXT("Grass") :
        TypeInfo->FoliageType == EAuracronFoliageType::Interactive ? TEXT("Interactive") :
        TEXT("Procedural"));

    DebugString += FString::Printf(TEXT("Density: %.2f\n"), TypeInfo->Density);
    DebugString += FString::Printf(TEXT("Radius: %.2f\n"), TypeInfo->Radius);
    DebugString += FString::Printf(TEXT("Collision: %s\n"), TypeInfo->bEnableCollision ? TEXT("Enabled") : TEXT("Disabled"));
    DebugString += FString::Printf(TEXT("Cull Distance: %.2f - %.2f\n"), TypeInfo->CullDistanceMin, TypeInfo->CullDistanceMax);

    // Add instance count
    int32 InstanceCount = GetFoliageInstanceCount(TypeName);
    DebugString += FString::Printf(TEXT("Instance Count: %d\n"), InstanceCount);

    return DebugString;
}

bool UAuracronFoliageBridgeAPI::ValidateFoliageIntegrity(const FString& TypeName) const
{
    if (!IsInitialized() || !ValidateTypeName(TypeName))
    {
        return false;
    }

    FScopeLock Lock(&FoliageMutex);

    const FAuracronFoliageTypeInfo* TypeInfo = FoliageTypeInfos.Find(TypeName);
    if (!TypeInfo)
    {
        return false;
    }

    UFoliageType* FoliageType = RegisteredFoliageTypes.FindRef(TypeName);
    if (!IsValid(FoliageType))
    {
        UE_LOG(LogAuracronFoliageBridge, Warning, TEXT("ValidateFoliageIntegrity: FoliageType is invalid for %s"), *TypeName);
        return false;
    }

    AInstancedFoliageActor* FoliageActor = FindFoliageActor(TypeName);
    if (!IsValid(FoliageActor))
    {
        UE_LOG(LogAuracronFoliageBridge, Warning, TEXT("ValidateFoliageIntegrity: FoliageActor not found for %s"), *TypeName);
        return false;
    }

    // Validate component integrity
    for (const auto& FoliagePair : FoliageActor->GetFoliageInfos())
    {
        if (FoliagePair.Key == FoliageType)
        {
            UHierarchicalInstancedStaticMeshComponent* Component = FoliagePair.Value->GetComponent();
            if (!IsValid(Component))
            {
                UE_LOG(LogAuracronFoliageBridge, Warning, TEXT("ValidateFoliageIntegrity: Component is invalid for %s"), *TypeName);
                return false;
            }

            // Check if instance count is reasonable
            int32 InstanceCount = Component->GetInstanceCount();
            if (InstanceCount < 0)
            {
                UE_LOG(LogAuracronFoliageBridge, Warning, TEXT("ValidateFoliageIntegrity: Invalid instance count %d for %s"), InstanceCount, *TypeName);
                return false;
            }

            break;
        }
    }

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Foliage integrity validation passed for type %s"), *TypeName);
    return true;
}

// Utility System Implementations
bool UAuracronFoliageBridgeAPI::SaveFoliageConfiguration(const FString& ConfigurationName, const FString& FilePath)
{
    SCOPE_CYCLE_COUNTER(STAT_AuracronFoliageBridge_UpdateInstance);

    if (!IsInitialized())
    {
        LogError(TEXT("SaveFoliageConfiguration: System not initialized"), 1036);
        return false;
    }

    if (ConfigurationName.IsEmpty() || FilePath.IsEmpty())
    {
        LogError(TEXT("SaveFoliageConfiguration: Invalid configuration name or file path"), 1037);
        return false;
    }

    FScopeLock Lock(&FoliageMutex);

    // Create configuration data
    FString ConfigData = FString::Printf(TEXT("# Auracron Foliage Configuration: %s\n"), *ConfigurationName);
    ConfigData += FString::Printf(TEXT("# Generated: %s\n\n"), *FDateTime::Now().ToString());

    // Save all registered foliage types
    for (const auto& TypePair : FoliageTypeInfos)
    {
        const FString& TypeName = TypePair.Key;
        const FAuracronFoliageTypeInfo& TypeInfo = TypePair.Value;

        ConfigData += FString::Printf(TEXT("[FoliageType:%s]\n"), *TypeName);
        ConfigData += FString::Printf(TEXT("Density=%.6f\n"), TypeInfo.Density);
        ConfigData += FString::Printf(TEXT("Radius=%.6f\n"), TypeInfo.Radius);
        ConfigData += FString::Printf(TEXT("ScaleMin=%.6f,%.6f,%.6f\n"), TypeInfo.ScaleMin.X, TypeInfo.ScaleMin.Y, TypeInfo.ScaleMin.Z);
        ConfigData += FString::Printf(TEXT("ScaleMax=%.6f,%.6f,%.6f\n"), TypeInfo.ScaleMax.X, TypeInfo.ScaleMax.Y, TypeInfo.ScaleMax.Z);
        ConfigData += FString::Printf(TEXT("AlignToNormal=%s\n"), TypeInfo.bAlignToNormal ? TEXT("true") : TEXT("false"));
        ConfigData += FString::Printf(TEXT("RandomYaw=%s\n"), TypeInfo.bRandomYaw ? TEXT("true") : TEXT("false"));
        ConfigData += FString::Printf(TEXT("EnableCollision=%s\n"), TypeInfo.bEnableCollision ? TEXT("true") : TEXT("false"));
        ConfigData += FString::Printf(TEXT("CullDistanceMin=%.6f\n"), TypeInfo.CullDistanceMin);
        ConfigData += FString::Printf(TEXT("CullDistanceMax=%.6f\n"), TypeInfo.CullDistanceMax);
        ConfigData += TEXT("\n");
    }

    // Write to file
    if (!FFileHelper::SaveStringToFile(ConfigData, *FilePath))
    {
        LogError(FString::Printf(TEXT("SaveFoliageConfiguration: Failed to write to file %s"), *FilePath), 1038);
        return false;
    }

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Saved foliage configuration '%s' to %s"), *ConfigurationName, *FilePath);
    return true;
}

bool UAuracronFoliageBridgeAPI::LoadFoliageConfiguration(const FString& ConfigurationName, const FString& FilePath)
{
    SCOPE_CYCLE_COUNTER(STAT_AuracronFoliageBridge_UpdateInstance);

    if (!IsInitialized())
    {
        LogError(TEXT("LoadFoliageConfiguration: System not initialized"), 1039);
        return false;
    }

    if (ConfigurationName.IsEmpty() || FilePath.IsEmpty())
    {
        LogError(TEXT("LoadFoliageConfiguration: Invalid configuration name or file path"), 1040);
        return false;
    }

    // Read file
    FString ConfigData;
    if (!FFileHelper::LoadFileToString(ConfigData, *FilePath))
    {
        LogError(FString::Printf(TEXT("LoadFoliageConfiguration: Failed to read file %s"), *FilePath), 1041);
        return false;
    }

    // Parse configuration data (simplified implementation)
    TArray<FString> Lines;
    ConfigData.ParseIntoArrayLines(Lines);

    FString CurrentTypeName;
    FAuracronFoliageTypeInfo CurrentTypeInfo;
    bool bInTypeSection = false;

    for (const FString& Line : Lines)
    {
        FString TrimmedLine = Line.TrimStartAndEnd();

        if (TrimmedLine.IsEmpty() || TrimmedLine.StartsWith(TEXT("#")))
        {
            continue;
        }

        if (TrimmedLine.StartsWith(TEXT("[FoliageType:")))
        {
            // Save previous type if valid
            if (bInTypeSection && !CurrentTypeName.IsEmpty())
            {
                RegisterFoliageType(CurrentTypeInfo);
            }

            // Start new type
            CurrentTypeName = TrimmedLine.Mid(13, TrimmedLine.Len() - 14); // Remove [FoliageType: and ]
            CurrentTypeInfo = FAuracronFoliageTypeInfo();
            CurrentTypeInfo.TypeName = CurrentTypeName;
            bInTypeSection = true;
        }
        else if (bInTypeSection && TrimmedLine.Contains(TEXT("=")))
        {
            FString Key, Value;
            if (TrimmedLine.Split(TEXT("="), &Key, &Value))
            {
                if (Key == TEXT("Density"))
                {
                    CurrentTypeInfo.Density = FCString::Atof(*Value);
                }
                else if (Key == TEXT("Radius"))
                {
                    CurrentTypeInfo.Radius = FCString::Atof(*Value);
                }
                // Add more parsing as needed
            }
        }
    }

    // Save last type
    if (bInTypeSection && !CurrentTypeName.IsEmpty())
    {
        RegisterFoliageType(CurrentTypeInfo);
    }

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Loaded foliage configuration '%s' from %s"), *ConfigurationName, *FilePath);
    return true;
}

bool UAuracronFoliageBridgeAPI::ExportFoliageData(const FString& TypeName, const FString& FilePath, const FString& Format)
{
    SCOPE_CYCLE_COUNTER(STAT_AuracronFoliageBridge_UpdateInstance);

    if (!IsInitialized() || !ValidateTypeName(TypeName))
    {
        LogError(TEXT("ExportFoliageData: Invalid state or type name"), 1042);
        return false;
    }

    if (FilePath.IsEmpty() || Format.IsEmpty())
    {
        LogError(TEXT("ExportFoliageData: Invalid file path or format"), 1043);
        return false;
    }

    FScopeLock Lock(&FoliageMutex);

    TArray<FAuracronFoliageInstanceInfo> Instances = GetFoliageInstances(TypeName);
    if (Instances.Num() == 0)
    {
        LogWarning(FString::Printf(TEXT("ExportFoliageData: No instances found for type %s"), *TypeName));
        return false;
    }

    FString ExportData;

    if (Format.ToLower() == TEXT("json"))
    {
        // Export as JSON format
        ExportData = TEXT("{\n");
        ExportData += FString::Printf(TEXT("  \"TypeName\": \"%s\",\n"), *TypeName);
        ExportData += FString::Printf(TEXT("  \"InstanceCount\": %d,\n"), Instances.Num());
        ExportData += TEXT("  \"Instances\": [\n");

        for (int32 i = 0; i < Instances.Num(); ++i)
        {
            const FAuracronFoliageInstanceInfo& Instance = Instances[i];
            ExportData += TEXT("    {\n");
            ExportData += FString::Printf(TEXT("      \"Index\": %d,\n"), Instance.InstanceIndex);
            ExportData += FString::Printf(TEXT("      \"Location\": [%.6f, %.6f, %.6f],\n"),
                Instance.Location.X, Instance.Location.Y, Instance.Location.Z);
            ExportData += FString::Printf(TEXT("      \"Rotation\": [%.6f, %.6f, %.6f],\n"),
                Instance.Rotation.Pitch, Instance.Rotation.Yaw, Instance.Rotation.Roll);
            ExportData += FString::Printf(TEXT("      \"Scale\": [%.6f, %.6f, %.6f],\n"),
                Instance.Scale.X, Instance.Scale.Y, Instance.Scale.Z);
            ExportData += FString::Printf(TEXT("      \"Health\": %.6f,\n"), Instance.Health);
            ExportData += FString::Printf(TEXT("      \"Age\": %.6f\n"), Instance.Age);
            ExportData += (i < Instances.Num() - 1) ? TEXT("    },\n") : TEXT("    }\n");
        }

        ExportData += TEXT("  ]\n");
        ExportData += TEXT("}\n");
    }
    else if (Format.ToLower() == TEXT("csv"))
    {
        // Export as CSV format
        ExportData = TEXT("Index,LocationX,LocationY,LocationZ,RotationPitch,RotationYaw,RotationRoll,ScaleX,ScaleY,ScaleZ,Health,Age\n");

        for (const FAuracronFoliageInstanceInfo& Instance : Instances)
        {
            ExportData += FString::Printf(TEXT("%d,%.6f,%.6f,%.6f,%.6f,%.6f,%.6f,%.6f,%.6f,%.6f,%.6f,%.6f\n"),
                Instance.InstanceIndex,
                Instance.Location.X, Instance.Location.Y, Instance.Location.Z,
                Instance.Rotation.Pitch, Instance.Rotation.Yaw, Instance.Rotation.Roll,
                Instance.Scale.X, Instance.Scale.Y, Instance.Scale.Z,
                Instance.Health, Instance.Age);
        }
    }
    else
    {
        LogError(FString::Printf(TEXT("ExportFoliageData: Unsupported format %s"), *Format), 1044);
        return false;
    }

    if (!FFileHelper::SaveStringToFile(ExportData, *FilePath))
    {
        LogError(FString::Printf(TEXT("ExportFoliageData: Failed to write to file %s"), *FilePath), 1045);
        return false;
    }

    UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Exported %d instances of type %s to %s"),
        Instances.Num(), *TypeName, *FilePath);
    return true;
}

bool UAuracronFoliageBridgeAPI::ImportFoliageData(const FString& TypeName, const FString& FilePath, const FString& Format)
{
    SCOPE_CYCLE_COUNTER(STAT_AuracronFoliageBridge_UpdateInstance);

    if (!IsInitialized() || !ValidateTypeName(TypeName))
    {
        LogError(TEXT("ImportFoliageData: Invalid state or type name"), 1046);
        return false;
    }

    if (FilePath.IsEmpty() || Format.IsEmpty())
    {
        LogError(TEXT("ImportFoliageData: Invalid file path or format"), 1047);
        return false;
    }

    FString ImportData;
    if (!FFileHelper::LoadFileToString(ImportData, *FilePath))
    {
        LogError(FString::Printf(TEXT("ImportFoliageData: Failed to read file %s"), *FilePath), 1048);
        return false;
    }

    TArray<FAuracronFoliageInstanceInfo> InstancesToAdd;

    if (Format.ToLower() == TEXT("csv"))
    {
        // Parse CSV format
        TArray<FString> Lines;
        ImportData.ParseIntoArrayLines(Lines);

        // Skip header line
        for (int32 i = 1; i < Lines.Num(); ++i)
        {
            TArray<FString> Values;
            Lines[i].ParseIntoArray(Values, TEXT(","));

            if (Values.Num() >= 12)
            {
                FAuracronFoliageInstanceInfo Instance;
                Instance.InstanceIndex = FCString::Atoi(*Values[0]);
                Instance.Location.X = FCString::Atof(*Values[1]);
                Instance.Location.Y = FCString::Atof(*Values[2]);
                Instance.Location.Z = FCString::Atof(*Values[3]);
                Instance.Rotation.Pitch = FCString::Atof(*Values[4]);
                Instance.Rotation.Yaw = FCString::Atof(*Values[5]);
                Instance.Rotation.Roll = FCString::Atof(*Values[6]);
                Instance.Scale.X = FCString::Atof(*Values[7]);
                Instance.Scale.Y = FCString::Atof(*Values[8]);
                Instance.Scale.Z = FCString::Atof(*Values[9]);
                Instance.Health = FCString::Atof(*Values[10]);
                Instance.Age = FCString::Atof(*Values[11]);
                Instance.Transform = FTransform(Instance.Rotation, Instance.Location, Instance.Scale);
                Instance.bIsValid = true;

                InstancesToAdd.Add(Instance);
            }
        }
    }
    else
    {
        LogError(FString::Printf(TEXT("ImportFoliageData: Unsupported format %s"), *Format), 1049);
        return false;
    }

    if (InstancesToAdd.Num() > 0)
    {
        TArray<int32> AddedIndices = AddFoliageInstances(TypeName, InstancesToAdd);
        UE_LOG(LogAuracronFoliageBridge, Log, TEXT("Imported %d instances of type %s from %s"),
            AddedIndices.Num(), *TypeName, *FilePath);
        return AddedIndices.Num() > 0;
    }

    return false;
}

TArray<FString> UAuracronFoliageBridgeAPI::GetSupportedExportFormats() const
{
    TArray<FString> SupportedFormats;
    SupportedFormats.Add(TEXT("JSON"));
    SupportedFormats.Add(TEXT("CSV"));
    return SupportedFormats;
}

TArray<FString> UAuracronFoliageBridgeAPI::GetSupportedImportFormats() const
{
    TArray<FString> SupportedFormats;
    SupportedFormats.Add(TEXT("CSV"));
    return SupportedFormats;
}

bool UAuracronFoliageBridgeAPI::BackupFoliageData(const FString& BackupName)
{
    if (!IsInitialized())
    {
        LogError(TEXT("BackupFoliageData: System not initialized"), 1050);
        return false;
    }

    if (BackupName.IsEmpty())
    {
        LogError(TEXT("BackupFoliageData: Invalid backup name"), 1051);
        return false;
    }

    FString BackupPath = FPaths::ProjectSavedDir() / TEXT("FoliageBackups") / BackupName;
    FString ConfigPath = BackupPath + TEXT(".cfg");

    return SaveFoliageConfiguration(BackupName, ConfigPath);
}

bool UAuracronFoliageBridgeAPI::RestoreFoliageData(const FString& BackupName)
{
    if (!IsInitialized())
    {
        LogError(TEXT("RestoreFoliageData: System not initialized"), 1052);
        return false;
    }

    if (BackupName.IsEmpty())
    {
        LogError(TEXT("RestoreFoliageData: Invalid backup name"), 1053);
        return false;
    }

    FString BackupPath = FPaths::ProjectSavedDir() / TEXT("FoliageBackups") / BackupName;
    FString ConfigPath = BackupPath + TEXT(".cfg");

    return LoadFoliageConfiguration(BackupName, ConfigPath);
}

#undef LOCTEXT_NAMESPACE

IMPLEMENT_MODULE(FAuracronFoliageBridgeModule, AuracronFoliageBridge)
